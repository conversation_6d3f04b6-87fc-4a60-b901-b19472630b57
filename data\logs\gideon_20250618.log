2025-06-18 17:24:14,869 - <PERSON><PERSON><PERSON> - INFO - 🤖 Starting Gideon AI Assistant
2025-06-18 17:24:14,872 - <PERSON><PERSON><PERSON> - INFO - Initializing Gideon Core System...
2025-06-18 17:24:14,878 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-18 17:24:14,878 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-18 17:24:14,879 - MemorySystem - INFO - Memory system initialized successfully
2025-06-18 17:24:14,880 - ModelManager - INFO - No existing models config found
2025-06-18 17:24:14,881 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 17:24:14,881 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 17:24:14,883 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-18 17:24:14,883 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 17:24:14,883 - <PERSON><PERSON><PERSON>ine - INFO - ✅ Ollama backend available
2025-06-18 17:24:14,884 - <PERSON>E<PERSON>ine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 17:24:14,884 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 17:24:14,884 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 17:24:14,885 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 17:24:14,885 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 17:24:15,146 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 17:24:15,147 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 17:24:15,147 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 17:24:15,147 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 17:24:15,150 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 17:24:15,151 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 17:24:15,151 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 17:24:15,260 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-18 17:24:15,317 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-18 17:24:15,454 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-18 17:24:15,454 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-18 17:24:18,464 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-18 17:24:18,482 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-18 17:24:18,483 - STTEngine - INFO -    Energy threshold: 150
2025-06-18 17:24:18,483 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-18 17:24:18,483 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-18 17:24:18,483 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-18 17:24:18,484 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-18 17:24:18,484 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-18 17:24:18,484 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-18 17:24:18,484 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-18 17:24:18,825 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-18 17:24:18,826 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-18 17:24:18,826 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-18 17:24:22,101 - STTEngine - INFO - Testing microphone... Say something!
2025-06-18 17:24:25,128 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-18 17:24:29,329 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 17:24:29,330 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-18 17:24:29,330 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-18 17:24:29,330 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-18 17:24:30,252 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 17:24:30,412 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-18 17:24:30,586 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-18 17:24:30,587 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-18 17:24:30,626 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-18 17:24:30,628 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-18 17:24:30,629 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-18 17:24:30,633 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-18 17:24:30,634 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: Gideon A...
2025-06-18 17:24:30,822 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,822 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,823 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-18 17:24:30,823 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: Ultra-pr...
2025-06-18 17:24:30,824 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,824 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,824 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-18 17:24:30,824 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: Bilingua...
2025-06-18 17:24:30,825 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,825 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,825 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-18 17:24:30,826 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: Voice in...
2025-06-18 17:24:30,826 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,826 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,827 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-18 17:24:30,827 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: Ultra-lo...
2025-06-18 17:24:30,828 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,828 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,828 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-18 17:24:30,829 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: AI Model...
2025-06-18 17:24:30,829 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,829 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,830 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-18 17:24:30,830 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: Advanced...
2025-06-18 17:24:30,830 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,830 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,831 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-18 17:24:30,831 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: Drag & d...
2025-06-18 17:24:30,831 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,831 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,831 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-18 17:24:30,832 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: 🚀 Gideon...
2025-06-18 17:24:30,841 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,841 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,841 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-18 17:24:30,842 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: 💼 Ultra-...
2025-06-18 17:24:30,846 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,847 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,847 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-18 17:24:30,847 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: 🎯 Real-t...
2025-06-18 17:24:30,851 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,851 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,851 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-18 17:24:30,851 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: 🧠 Enterp...
2025-06-18 17:24:30,852 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,852 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,853 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-18 17:24:30,853 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: 💡 Advanc...
2025-06-18 17:24:30,857 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,857 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,857 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-18 17:24:30,857 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: 💬 Say 'G...
2025-06-18 17:24:30,858 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,858 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:32,868 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-18 17:24:32,869 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:32] 🤖 Gideon: Hello! I'...
2025-06-18 17:24:32,871 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:32,871 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:33:51,064 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-18 17:33:51,074 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat activated
2025-06-18 17:33:54,765 - CompactChatManager - INFO - Compact chat window hidden
2025-06-18 17:33:54,765 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Compact chat closed. Main window is still runnin...
2025-06-18 17:33:54,768 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:33:54] ⚙️ System: 💬 Compac...
2025-06-18 17:33:54,862 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-18 17:33:54,863 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat shown for continued access
2025-06-18 17:33:54,874 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:33:54,875 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:33:57,936 - CompactChatManager - INFO - Compact chat window hidden
2025-06-18 17:33:57,936 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Compact chat closed. Main window is still runnin...
2025-06-18 17:33:57,936 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:33:57] ⚙️ System: 💬 Compac...
2025-06-18 17:33:57,939 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:33:57,939 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:14,657 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant
2025-06-18 17:41:14,659 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-18 17:41:14,665 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-18 17:41:14,665 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-18 17:41:14,665 - MemorySystem - INFO - Memory system initialized successfully
2025-06-18 17:41:14,667 - ModelManager - INFO - No existing models config found
2025-06-18 17:41:14,667 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 17:41:14,667 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 17:41:14,668 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-18 17:41:14,669 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 17:41:14,669 - AIEngine - INFO - ✅ Ollama backend available
2025-06-18 17:41:14,669 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 17:41:14,669 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 17:41:14,670 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 17:41:14,670 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 17:41:14,670 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 17:41:14,898 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 17:41:14,899 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 17:41:14,899 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 17:41:14,899 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 17:41:14,901 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 17:41:14,901 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 17:41:14,901 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 17:41:14,991 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-18 17:41:15,048 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-18 17:41:15,171 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-18 17:41:15,171 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-18 17:41:18,168 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-18 17:41:18,179 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-18 17:41:18,180 - STTEngine - INFO -    Energy threshold: 150
2025-06-18 17:41:18,180 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-18 17:41:18,180 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-18 17:41:18,180 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-18 17:41:18,180 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-18 17:41:18,180 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-18 17:41:18,180 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-18 17:41:18,180 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-18 17:41:18,404 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-18 17:41:18,404 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-18 17:41:18,404 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-18 17:41:21,666 - STTEngine - INFO - Testing microphone... Say something!
2025-06-18 17:41:24,674 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-18 17:41:28,880 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 17:41:28,881 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-18 17:41:28,881 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-18 17:41:28,881 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-18 17:41:29,586 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 17:41:29,714 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-18 17:41:29,888 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-18 17:41:29,889 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-18 17:41:29,929 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-18 17:41:29,933 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-18 17:41:29,933 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-18 17:41:29,936 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-18 17:41:29,936 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:29] ⚙️ System: Gideon A...
2025-06-18 17:41:30,172 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,172 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,172 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-18 17:41:30,173 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: Ultra-pr...
2025-06-18 17:41:30,173 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,173 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,173 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-18 17:41:30,173 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: Bilingua...
2025-06-18 17:41:30,174 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,174 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,174 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-18 17:41:30,174 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: Voice in...
2025-06-18 17:41:30,174 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,175 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,175 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-18 17:41:30,175 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: Ultra-lo...
2025-06-18 17:41:30,175 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,176 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,176 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-18 17:41:30,176 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: AI Model...
2025-06-18 17:41:30,176 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,176 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,177 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-18 17:41:30,177 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: Advanced...
2025-06-18 17:41:30,177 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,177 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,177 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-18 17:41:30,178 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: Drag & d...
2025-06-18 17:41:30,178 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,178 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,178 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-18 17:41:30,179 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: 🚀 Gideon...
2025-06-18 17:41:30,187 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,187 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,187 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-18 17:41:30,188 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: 💼 Ultra-...
2025-06-18 17:41:30,191 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,191 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,191 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-18 17:41:30,191 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: 🎯 Real-t...
2025-06-18 17:41:30,195 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,195 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,195 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-18 17:41:30,195 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: 🧠 Enterp...
2025-06-18 17:41:30,196 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,196 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,196 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-18 17:41:30,196 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: 💡 Advanc...
2025-06-18 17:41:30,200 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,200 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,200 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-18 17:41:30,200 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: 💬 Say 'G...
2025-06-18 17:41:30,201 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,202 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:32,209 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-18 17:41:32,209 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:32] 🤖 Gideon: Hello! I'...
2025-06-18 17:41:32,210 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:32,210 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:47:47,812 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-18 17:47:47,822 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat activated
2025-06-18 17:48:07,450 - CompactChatManager - INFO - Compact chat window hidden
2025-06-18 17:48:07,451 - UltraProfessionalInterface - INFO - 🔄 Window restored - compact chat hidden
2025-06-18 17:48:24,259 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-18 17:48:24,259 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat activated
2025-06-18 17:48:46,501 - CompactChatManager - INFO - Compact chat window hidden
2025-06-18 17:48:46,501 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Compact chat closed. Main window is still runnin...
2025-06-18 17:48:46,505 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:48:46] ⚙️ System: 💬 Compac...
2025-06-18 17:48:46,510 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:48:46,511 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:48:53,052 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-18 17:48:53,053 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat activated
2025-06-18 17:48:55,130 - CompactChatManager - INFO - Compact chat window hidden
2025-06-18 17:48:55,130 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Compact chat closed. Main window is still runnin...
2025-06-18 17:48:55,130 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:48:55] ⚙️ System: 💬 Compac...
2025-06-18 17:48:55,137 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:48:55,137 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:48:59,767 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-18 17:48:59,786 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat shown for continued access
2025-06-18 17:49:35,190 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant
2025-06-18 17:49:35,192 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-18 17:49:35,198 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-18 17:49:35,198 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-18 17:49:35,198 - MemorySystem - INFO - Memory system initialized successfully
2025-06-18 17:49:35,200 - ModelManager - INFO - No existing models config found
2025-06-18 17:49:35,200 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 17:49:35,201 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 17:49:35,202 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-18 17:49:35,202 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 17:49:35,202 - AIEngine - INFO - ✅ Ollama backend available
2025-06-18 17:49:35,202 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 17:49:35,202 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 17:49:35,203 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 17:49:35,203 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 17:49:35,203 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 17:49:35,448 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 17:49:35,449 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 17:49:35,449 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 17:49:35,450 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 17:49:35,453 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 17:49:35,453 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 17:49:35,453 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 17:49:35,553 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-18 17:49:35,611 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-18 17:49:35,731 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-18 17:49:35,731 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-18 17:49:38,728 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-18 17:49:38,740 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-18 17:49:38,740 - STTEngine - INFO -    Energy threshold: 150
2025-06-18 17:49:38,740 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-18 17:49:38,740 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-18 17:49:38,741 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-18 17:49:38,741 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-18 17:49:38,741 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-18 17:49:38,741 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-18 17:49:38,741 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-18 17:49:38,982 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-18 17:49:38,982 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-18 17:49:38,983 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-18 17:49:42,302 - STTEngine - INFO - Testing microphone... Say something!
2025-06-18 17:49:45,313 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-18 17:49:49,513 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 17:49:49,514 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-18 17:49:49,514 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-18 17:49:49,514 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-18 17:49:50,223 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 17:49:50,355 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-18 17:49:50,525 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-18 17:49:50,525 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-18 17:49:50,566 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-18 17:49:50,568 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-18 17:49:50,568 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-18 17:49:50,573 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-18 17:49:50,573 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: Gideon A...
2025-06-18 17:49:50,773 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,773 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,773 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-18 17:49:50,773 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: Ultra-pr...
2025-06-18 17:49:50,775 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,775 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,775 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-18 17:49:50,776 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: Bilingua...
2025-06-18 17:49:50,778 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,778 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,778 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-18 17:49:50,778 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: Voice in...
2025-06-18 17:49:50,780 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,780 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,781 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-18 17:49:50,781 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: Ultra-lo...
2025-06-18 17:49:50,783 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,783 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,784 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-18 17:49:50,784 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: AI Model...
2025-06-18 17:49:50,786 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,786 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,786 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-18 17:49:50,786 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: Advanced...
2025-06-18 17:49:50,788 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,788 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,788 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-18 17:49:50,788 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: Drag & d...
2025-06-18 17:49:50,798 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,798 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,798 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-18 17:49:50,798 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: 🚀 Gideon...
2025-06-18 17:49:50,808 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,808 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,809 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-18 17:49:50,809 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: 💼 Ultra-...
2025-06-18 17:49:50,815 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,815 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,815 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-18 17:49:50,815 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: 🎯 Real-t...
2025-06-18 17:49:50,820 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,821 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,821 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-18 17:49:50,821 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: 🧠 Enterp...
2025-06-18 17:49:50,824 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,824 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,824 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-18 17:49:50,825 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: 💡 Advanc...
2025-06-18 17:49:50,831 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,831 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,831 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-18 17:49:50,831 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: 💬 Say 'G...
2025-06-18 17:49:50,835 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,835 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:52,848 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-18 17:49:52,848 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:52] 🤖 Gideon: Hello! I'...
2025-06-18 17:49:52,853 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:52,853 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:50:29,206 - GideonCore - INFO - Always listening stopped
2025-06-18 17:50:29,870 - STTEngine - INFO - Stopped continuous listening
2025-06-18 17:51:02,727 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-18 17:51:02,733 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat activated
2025-06-18 17:51:15,369 - CompactChatManager - INFO - Compact chat window hidden
2025-06-18 17:51:15,370 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Compact chat closed. Main window is still runnin...
2025-06-18 17:51:15,375 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:51:15] ⚙️ System: 💬 Compac...
2025-06-18 17:51:15,397 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:51:15,398 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:51:20,167 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-18 17:51:20,186 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat shown for continued access
2025-06-18 17:52:24,132 - CompactChatManager - INFO - Compact chat window hidden
2025-06-18 17:52:24,134 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Compact chat closed. Main window is still runnin...
2025-06-18 17:52:24,137 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:52:24] ⚙️ System: 💬 Compac...
2025-06-18 17:52:24,147 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:52:24,147 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:14,482 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant
2025-06-18 18:33:14,484 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-18 18:33:14,489 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-18 18:33:14,489 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-18 18:33:14,489 - MemorySystem - INFO - Memory system initialized successfully
2025-06-18 18:33:14,490 - ModelManager - INFO - No existing models config found
2025-06-18 18:33:14,491 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 18:33:14,491 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 18:33:14,493 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-18 18:33:14,493 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 18:33:14,493 - AIEngine - INFO - ✅ Ollama backend available
2025-06-18 18:33:14,494 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 18:33:14,494 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 18:33:14,495 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 18:33:14,495 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 18:33:14,495 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 18:33:14,718 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 18:33:14,719 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 18:33:14,720 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 18:33:14,720 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 18:33:14,723 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 18:33:14,723 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 18:33:14,724 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 18:33:14,835 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-18 18:33:14,893 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-18 18:33:15,020 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-18 18:33:15,021 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-18 18:33:18,031 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-18 18:33:18,048 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-18 18:33:18,048 - STTEngine - INFO -    Energy threshold: 150
2025-06-18 18:33:18,048 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-18 18:33:18,049 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-18 18:33:18,049 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-18 18:33:18,049 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-18 18:33:18,049 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-18 18:33:18,049 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-18 18:33:18,049 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-18 18:33:18,275 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-18 18:33:18,275 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-18 18:33:18,276 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-18 18:33:21,550 - STTEngine - INFO - Testing microphone... Say something!
2025-06-18 18:33:24,577 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-18 18:33:28,776 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 18:33:28,777 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-18 18:33:28,777 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-18 18:33:28,777 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-18 18:33:29,455 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 18:33:29,581 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-18 18:33:29,747 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-18 18:33:29,747 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-18 18:33:29,785 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-18 18:33:29,787 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-18 18:33:29,787 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-18 18:33:29,792 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-18 18:33:29,792 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:29] ⚙️ System: Gideon A...
2025-06-18 18:33:29,978 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:29,978 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:29,978 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-18 18:33:29,978 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:29] ⚙️ System: Ultra-pr...
2025-06-18 18:33:29,981 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:29,981 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:29,981 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-18 18:33:29,981 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:29] ⚙️ System: Bilingua...
2025-06-18 18:33:29,983 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:29,984 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:29,984 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-18 18:33:29,984 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:29] ⚙️ System: Voice in...
2025-06-18 18:33:29,987 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:29,987 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:29,987 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-18 18:33:29,987 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:29] ⚙️ System: Ultra-lo...
2025-06-18 18:33:29,990 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:29,991 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:29,991 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-18 18:33:29,991 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:29] ⚙️ System: AI Model...
2025-06-18 18:33:29,992 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:29,993 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:29,993 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-18 18:33:29,993 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:29] ⚙️ System: Advanced...
2025-06-18 18:33:29,995 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:29,995 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:29,995 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-18 18:33:29,995 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:29] ⚙️ System: Drag & d...
2025-06-18 18:33:29,997 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:29,997 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:29,997 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-18 18:33:29,998 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:29] ⚙️ System: 🚀 Gideon...
2025-06-18 18:33:30,012 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:30,012 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:30,013 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-18 18:33:30,013 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:30] ⚙️ System: 💼 Ultra-...
2025-06-18 18:33:30,020 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:30,021 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:30,021 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-18 18:33:30,021 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:30] ⚙️ System: 🎯 Real-t...
2025-06-18 18:33:30,027 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:30,027 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:30,027 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-18 18:33:30,027 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:30] ⚙️ System: 🧠 Enterp...
2025-06-18 18:33:30,030 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:30,030 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:30,030 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-18 18:33:30,031 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:30] ⚙️ System: 💡 Advanc...
2025-06-18 18:33:30,038 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:30,039 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:30,039 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-18 18:33:30,039 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:30] ⚙️ System: 💬 Say 'G...
2025-06-18 18:33:30,042 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:30,042 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:32,050 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-18 18:33:32,051 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:32] 🤖 Gideon: Hello! I'...
2025-06-18 18:33:32,054 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:32,055 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:37,029 - AIEngine - INFO - Found Ollama models: ['qwen3:235b', 'gemma3:27b', 'deepseek-coder-v2:236b', 'dolphin-llama3:70b']
2025-06-18 18:34:21,080 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-18 18:34:21,098 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat shown for continued access
2025-06-18 18:34:25,245 - CompactChatManager - INFO - Compact chat window hidden
2025-06-18 18:34:25,247 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Compact chat closed. Main window is still runnin...
2025-06-18 18:34:25,249 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:34:25] ⚙️ System: 💬 Compac...
2025-06-18 18:34:25,255 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:34:25,255 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 19:31:37,104 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant Enterprise Edition
2025-06-18 19:31:48,121 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-18 19:31:48,127 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-18 19:31:48,127 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-18 19:31:48,128 - MemorySystem - INFO - Memory system initialized successfully
2025-06-18 19:31:48,129 - ModelManager - INFO - No existing models config found
2025-06-18 19:31:48,130 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 19:31:48,130 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 19:31:48,131 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-18 19:31:48,131 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 19:31:48,132 - AIEngine - INFO - ✅ Ollama backend available
2025-06-18 19:31:48,132 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 19:31:48,132 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 19:31:48,133 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 19:31:48,133 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 19:31:48,133 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 19:31:48,372 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 19:31:48,374 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 19:31:48,374 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 19:31:48,374 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 19:31:48,376 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 19:31:48,376 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 19:31:48,377 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 19:31:48,485 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-18 19:31:48,540 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-18 19:31:48,666 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-18 19:31:48,666 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-18 19:31:51,676 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-18 19:31:51,692 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-18 19:31:51,693 - STTEngine - INFO -    Energy threshold: 150
2025-06-18 19:31:51,693 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-18 19:31:51,693 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-18 19:31:51,693 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-18 19:31:51,694 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-18 19:31:51,694 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-18 19:31:51,694 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-18 19:31:51,694 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-18 19:31:51,944 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-18 19:31:51,945 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-18 19:31:51,945 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-18 19:31:55,210 - STTEngine - INFO - Testing microphone... Say something!
2025-06-18 19:31:58,238 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-18 19:32:02,446 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 19:32:02,447 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-18 19:32:02,447 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-18 19:32:02,447 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-18 19:32:02,588 - UltraProfessionalInterface - INFO - Detected DPI scaling: 1.00x
2025-06-18 19:32:02,836 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 19:32:02,964 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-18 19:32:03,153 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-18 19:32:03,153 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-18 19:32:03,194 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-18 19:32:03,196 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-18 19:32:03,197 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-18 19:32:03,200 - GideonMain - ERROR - Error during initialization: cannot access free variable '_add_enterprise_welcome_messages' where it is not associated with a value in enclosing scope
2025-06-18 19:32:19,891 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> هلا...
2025-06-18 19:32:20,187 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﻼﻫ :You 👤 [19:32:19]
...
2025-06-18 19:32:20,188 - UltraProfessionalInterface - INFO - 🔤 Arabic text processed: ﻼﻫ...
2025-06-18 19:32:20,442 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 19:32:20,443 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 19:32:20,864 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-18 19:32:20,897 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 19:32:20,897 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-18 19:32:20,898 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-18 19:32:20,899 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 19:32:20,899 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-18 19:32:20,900 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-18 19:32:20,900 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-18 19:32:20,901 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-18 19:32:20,902 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 19:32:20,902 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-18 19:32:21,301 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 19:32:21,646 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'أعمل على ذلك......'
2025-06-18 19:32:21,649 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'هلا' -> 'أعمل على ذلك......'
2025-06-18 19:32:22,768 - UltraProfessionalInterface - ERROR - Error stopping static thinking display: main thread is not in main loop
2025-06-18 19:32:23,853 - UltraProfessionalInterface - ERROR - Error handling text response: main thread is not in main loop
2025-06-18 19:32:23,854 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> ❌ Error displaying response: main thread is not in...
2025-06-18 19:32:23,854 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [19:32:23] ⚙️ System: ❌ Error ...
2025-06-18 19:32:24,950 - UltraProfessionalInterface - ERROR - Error inserting CustomTkinter message: main thread is not in main loop
2025-06-18 19:32:27,118 - UltraProfessionalInterface - ERROR - Fallback insertion also failed: main thread is not in main loop
2025-06-18 19:32:28,218 - UltraProfessionalInterface - ERROR - Optimized processing error: main thread is not in main loop
2025-06-18 19:32:28,249 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-18 19:32:28,249 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 19:32:28,250 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-18 19:32:28,250 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-18 19:32:28,250 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-18 19:32:28,250 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 19:32:51,122 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-18 19:32:51,158 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat shown for continued access
2025-06-18 19:34:30,111 - AIEngine - INFO - LLM response generated successfully: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:34:30,118 - AIEngine - INFO - LLM generated successful response: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:34:30,139 - AIEngine - INFO - Final response generated: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:34:43,264 - AIEngine - INFO - LLM response generated successfully: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:34:43,265 - AIEngine - INFO - LLM generated successful response: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:34:43,269 - AIEngine - INFO - Final response generated: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:34:52,464 - CompactChatManager - INFO - Compact chat window hidden
2025-06-18 19:34:52,467 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Compact chat closed. Main window is still runnin...
2025-06-18 19:34:52,474 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [19:34:52] ⚙️ System: 💬 Compac...
2025-06-18 19:34:52,568 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 19:34:52,569 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 19:34:53,058 - AIEngine - INFO - LLM response generated successfully: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:34:53,058 - AIEngine - INFO - LLM generated successful response: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:34:53,062 - AIEngine - INFO - Final response generated: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:34:53,066 - UltraProfessionalInterface - INFO - 🎯 FALLBACK 1 RESPONSE: 'مرحباً! كيف يمكنني مساعدتك اليوم؟...'
2025-06-18 19:34:53,067 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'هلا' -> 'مرحباً! كيف يمكنني مساعدتك اليوم؟...'
2025-06-18 19:34:54,163 - UltraProfessionalInterface - ERROR - Error stopping static thinking display: main thread is not in main loop
2025-06-18 19:34:55,250 - UltraProfessionalInterface - ERROR - Error handling text response: main thread is not in main loop
2025-06-18 19:34:55,250 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> ❌ Error displaying response: main thread is not in...
2025-06-18 19:34:55,251 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [19:34:55] ⚙️ System: ❌ Error ...
2025-06-18 19:34:56,337 - UltraProfessionalInterface - ERROR - Error inserting CustomTkinter message: main thread is not in main loop
2025-06-18 19:34:58,515 - UltraProfessionalInterface - ERROR - Fallback insertion also failed: main thread is not in main loop
2025-06-18 19:34:59,604 - UltraProfessionalInterface - ERROR - Direct AI processing error: main thread is not in main loop
2025-06-18 19:35:00,699 - GideonCore - ERROR - Error processing text request: main thread is not in main loop
2025-06-18 19:35:01,800 - GideonCore - ERROR - Worker loop error: main thread is not in main loop
2025-06-18 19:39:05,732 - EnterpriseSettings - INFO - Enterprise settings loaded successfully
2025-06-18 19:39:05,775 - EnterpriseDataManager - INFO - Database initialized successfully
2025-06-18 19:39:05,780 - EnterpriseHelpSystem - INFO - Help content files initialized
2025-06-18 19:40:09,761 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant Enterprise Edition
2025-06-18 19:40:22,823 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant Enterprise Edition
2025-06-18 19:40:33,739 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-18 19:40:33,741 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-18 19:40:33,742 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-18 19:40:33,742 - MemorySystem - INFO - Memory system initialized successfully
2025-06-18 19:40:33,743 - ModelManager - INFO - No existing models config found
2025-06-18 19:40:33,744 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 19:40:33,744 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 19:40:33,746 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-18 19:40:33,746 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 19:40:33,747 - AIEngine - INFO - ✅ Ollama backend available
2025-06-18 19:40:33,747 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 19:40:33,747 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 19:40:33,748 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 19:40:33,748 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 19:40:33,748 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 19:40:34,008 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 19:40:34,010 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 19:40:34,010 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 19:40:34,010 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 19:40:34,013 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 19:40:34,013 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 19:40:34,014 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 19:40:34,173 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-18 19:40:34,228 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-18 19:40:34,358 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-18 19:40:34,358 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-18 19:40:37,368 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-18 19:40:37,385 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-18 19:40:37,385 - STTEngine - INFO -    Energy threshold: 150
2025-06-18 19:40:37,385 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-18 19:40:37,386 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-18 19:40:37,386 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-18 19:40:37,386 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-18 19:40:37,386 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-18 19:40:37,386 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-18 19:40:37,386 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-18 19:40:37,654 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-18 19:40:37,655 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-18 19:40:37,655 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-18 19:40:40,943 - STTEngine - INFO - Testing microphone... Say something!
2025-06-18 19:40:43,971 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-18 19:40:48,171 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 19:40:48,172 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-18 19:40:48,173 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-18 19:40:48,173 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-18 19:40:48,304 - UltraProfessionalInterface - INFO - Detected DPI scaling: 1.00x
2025-06-18 19:40:48,546 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 19:40:48,670 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-18 19:40:48,840 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-18 19:40:48,841 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-18 19:40:48,879 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-18 19:40:48,882 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-18 19:40:48,882 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-18 19:40:48,886 - GideonMain - ERROR - Error during initialization: cannot access free variable '_add_enterprise_welcome_messages' where it is not associated with a value in enclosing scope
2025-06-18 19:41:56,808 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-18 19:41:56,812 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 19:41:56,813 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-18 19:41:56,813 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-18 19:41:56,813 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-18 19:41:56,814 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
