"""
Ultra Professional Interface for Gideon AI Assistant
Enterprise-grade design with advanced animations and professional patterns
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from datetime import datetime
from typing import Optional, Callable, Dict, Any
import threading
import time
import math

from src.utils.logger import <PERSON><PERSON><PERSON><PERSON>
from src.utils.config import Config
from src.utils.i18n import get_i18n
# Avatar system removed - no longer imported
from src.ui.voice_chat_interface import VoiceChatInterface
from src.ui.terminal_interface import TerminalInterface
from src.optimization.performance_optimizer import PerformanceManager
from src.utils.gender_consistency import gideon_identity
from src.utils.text_direction import text_direction_manager

try:
    import customtkinter as ctk
    CUSTOMTKINTER_AVAILABLE = True
    # Set ultra-professional appearance
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
except ImportError:
    CUSTOMTKINTER_AVAILABLE = False


class UltraProfessionalInterface:
    """Ultra professional enterprise-grade interface for Gideon AI Assistant"""
    
    def __init__(self, gideon_core=None):
        self.logger = GideonLogger("UltraProfessionalInterface")
        self.config = Config()
        self.i18n = get_i18n()
        self.gideon_core = gideon_core
        
        # Window setup
        self.root = None
        self.is_running = False
        
        # Ultra-professional design system - Enterprise grade with Full HD support
        self.design_system = {
            # Color palette - Enterprise dark theme with enhanced depth
            'colors': {
                # Background layers with depth and glass morphism
                'bg_primary': '#0a0e13',      # Deep space black
                'bg_secondary': '#0d1117',    # GitHub dark primary
                'bg_tertiary': '#161b22',     # GitHub dark secondary
                'bg_elevated': '#21262d',     # Elevated surfaces
                'bg_overlay': '#30363d',      # Modal overlays
                'bg_glass': '#1c212880',      # Glass morphism
                'bg_glass_light': '#30363d40', # Light glass effect
                'bg_gradient_start': '#0a0e13', # Gradient backgrounds
                'bg_gradient_end': '#161b22',
                
                # Accent colors - Professional palette
                'accent_primary': '#58a6ff',  # Professional blue
                'accent_secondary': '#f78166', # Coral accent
                'accent_success': '#56d364',  # Success green
                'accent_warning': '#e3b341',  # Warning amber
                'accent_error': '#f85149',    # Error red
                'accent_purple': '#bc8cff',   # Purple accent
                'accent_pink': '#ff7eb6',     # Pink accent
                'accent_cyan': '#39d0d8',     # Cyan accent
                
                # Text hierarchy - Perfect readability
                'text_primary': '#f0f6fc',    # Primary text
                'text_secondary': '#8b949e',  # Secondary text
                'text_muted': '#6e7681',      # Muted text
                'text_disabled': '#484f58',   # Disabled text
                'text_inverse': '#0a0e13',    # Inverse text
                'text_accent': '#58a6ff',     # Accent text
                
                # Interactive states
                'border_default': '#30363d',  # Default borders
                'border_muted': '#21262d',    # Muted borders
                'border_accent': '#58a6ff',   # Accent borders
                'hover_bg': '#262c36',        # Hover state
                'active_bg': '#2d333b',       # Active state
                'focus_ring': '#58a6ff60',    # Focus ring
                'selection_bg': '#58a6ff20',  # Selection background
                
                # Special effects
                'glow_primary': '#58a6ff40',  # Primary glow
                'glow_success': '#56d36440',  # Success glow
                'glow_error': '#f8514940',    # Error glow
                'glow_warning': '#e3b34140',  # Warning glow
                'shadow_light': '#ffffff08',  # Light shadow
                'shadow_dark': '#00000040',   # Dark shadow
                'gradient_start': '#58a6ff20', # Gradient start
                'gradient_end': '#bc8cff10',  # Gradient end
            },
            
            # Typography system - Enhanced for Full HD with DPI scaling
            'typography': {
                # Display fonts - Large headings
                'display_large': ('Segoe UI Variable Display', 32, 'bold'),
                'display_medium': ('Segoe UI Variable Display', 28, 'bold'),
                'display_small': ('Segoe UI Variable Display', 24, 'bold'),

                # Headline fonts - Section headers
                'headline_large': ('Segoe UI Variable', 22, 'bold'),
                'headline_medium': ('Segoe UI Variable', 18, 'bold'),
                'headline_small': ('Segoe UI Variable', 16, 'bold'),

                # Title fonts - Component titles
                'title_large': ('Segoe UI Variable', 14, 'bold'),
                'title_medium': ('Segoe UI Variable', 12, 'bold'),
                'title_small': ('Segoe UI Variable', 11, 'bold'),

                # Body fonts - Main content
                'body_large': ('Segoe UI Variable', 14, 'normal'),
                'body_medium': ('Segoe UI Variable', 12, 'normal'),
                'body_small': ('Segoe UI Variable', 11, 'normal'),

                # Label fonts - UI labels
                'label_large': ('Segoe UI Variable', 12, 'normal'),
                'label_medium': ('Segoe UI Variable', 11, 'normal'),
                'label_small': ('Segoe UI Variable', 10, 'normal'),

                # Code fonts - Monospace
                'code': ('Cascadia Code', 12, 'normal'),
                'code_small': ('Cascadia Code', 10, 'normal'),

                # Arabic fonts - RTL support
                'arabic_large': ('Segoe UI Historic', 14, 'normal'),
                'arabic_medium': ('Segoe UI Historic', 12, 'normal'),
                'arabic_small': ('Segoe UI Historic', 11, 'normal'),

                # Font properties
                'line_height': 1.5,
                'letter_spacing': 0.5,
                'font_weights': {
                    'light': 300,
                    'regular': 400,
                    'medium': 500,
                    'semibold': 600,
                    'bold': 700
                }
            },

            # Vector icons and scalable elements
            'icons': {
                'size_tiny': 12,
                'size_small': 16,
                'size_medium': 24,
                'size_large': 32,
                'size_xlarge': 48,
                'size_huge': 64,
                'stroke_width': 2,
                'style': 'outline',  # outline, filled, or duotone
                'library': 'lucide',  # Icon library preference
                'unicode_fallbacks': {
                    'ai': '🤖',
                    'voice': '🎤',
                    'settings': '⚙️',
                    'analytics': '📊',
                    'terminal': '💻',
                    'chat': '💬',
                    'models': '🧠',
                    'performance': '⚡',
                    'status': '📡',
                    'help': '❓',
                    'export': '📤',
                    'import': '📥',
                    'save': '💾',
                    'load': '📂'
                }
            },
            
            # Spacing system
            'spacing': {
                'xs': 4,
                'sm': 8,
                'md': 16,
                'lg': 24,
                'xl': 32,
                'xxl': 48,
                'xxxl': 64,
            },
            
            # Border radius system
            'radius': {
                'none': 0,
                'sm': 4,
                'md': 8,
                'lg': 12,
                'xl': 16,
                'xxl': 24,
                'full': 9999,
            },
            
            # Shadow system
            'shadows': {
                'sm': '0 1px 2px rgba(0, 0, 0, 0.05)',
                'md': '0 4px 6px rgba(0, 0, 0, 0.1)',
                'lg': '0 10px 15px rgba(0, 0, 0, 0.1)',
                'xl': '0 20px 25px rgba(0, 0, 0, 0.1)',
                'inner': 'inset 0 2px 4px rgba(0, 0, 0, 0.06)',
            },
            
            # Animation system - Enhanced for smooth transitions
            'animations': {
                'duration_fast': 150,
                'duration_normal': 300,
                'duration_slow': 500,
                'duration_very_slow': 800,
                'easing_ease': 'ease',
                'easing_ease_in': 'ease-in',
                'easing_ease_out': 'ease-out',
                'easing_ease_in_out': 'ease-in-out',
                'easing_bounce': 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
                'easing_smooth': 'cubic-bezier(0.4, 0, 0.2, 1)',
                'fade_steps': 20,  # Steps for fade animations
                'slide_distance': 30,  # Pixels for slide animations
            },

            # Glass morphism effects
            'glass': {
                'blur_radius': 10,
                'opacity': 0.1,
                'border_opacity': 0.2,
                'backdrop_filter': 'blur(10px)',
                'background_alpha': 0.05,
                'border_alpha': 0.15,
                'shadow_alpha': 0.3,
            },

            # Micro-animations and transitions
            'transitions': {
                'hover_scale': 1.02,
                'hover_opacity': 0.8,
                'active_scale': 0.98,
                'button_lift': 2,  # Shadow lift on hover
                'glow_intensity': 0.3,
                'pulse_scale': 1.05,
                'fade_in_delay': 50,  # ms between element fade-ins
            }
        }
        
        # UI Components
        self.main_container = None
        self.sidebar = None
        self.main_content = None
        self.status_bar = None
        self.floating_panels = {}
        
        # Chat components
        self.chat_display = None
        self.input_entry = None
        self.send_button = None
        self.voice_button = None
        
        # Status indicators
        self.ai_status_indicator = None
        self.voice_status_indicator = None
        self.memory_status_indicator = None
        self.performance_monitor = None
        
        # Animation state
        self.animation_running = False
        self.pulse_animation = False
        self.typing_animation = False
        
        # State management
        self.is_voice_active = False
        self.current_status = "initializing"
        self.ai_status = "offline"
        self.performance_data = {
            'cpu_usage': 0,
            'memory_usage': 0,
            'response_time': 0,
            'model_load': 0
        }
        
        # New ultra-professional components
        # Avatar manager removed - no visual avatar
        self.voice_chat_interface = None
        self.terminal_interface = None
        self.performance_manager = PerformanceManager()

        # Compact chat management for minimized chat access
        self.compact_chat = None
        self.minimized_to_compact = False

        # Setup callbacks
        if self.gideon_core:
            self.gideon_core.set_response_ready_callback(self._on_response_ready)
            self.gideon_core.set_status_change_callback(self._on_status_change)
            self.gideon_core.set_error_callback(self._on_error)
    
    def create_window(self):
        """Create the ultra-professional main window"""
        if CUSTOMTKINTER_AVAILABLE:
            self.root = ctk.CTk()
            self._setup_ultra_professional_window()
        else:
            self.root = tk.Tk()
            self._setup_fallback_window()
        
        self._create_ultra_professional_layout()
        self._setup_advanced_bindings()
        self._start_animations()
        self._initialize_performance_monitoring()
        self._initialize_compact_chat()

        return self.root
    
    def _setup_ultra_professional_window(self):
        """Setup ultra-professional CustomTkinter window with Full HD support"""
        self.root.title("🤖 GIDEON AI Assistant - Enterprise Edition")

        # Full HD (1920x1080) native resolution support
        self.root.geometry("1920x1080")
        self.root.minsize(1600, 900)

        # High-DPI scaling support
        try:
            # Enable high-DPI awareness for crisp rendering
            import ctypes
            ctypes.windll.shcore.SetProcessDpiAwareness(1)
        except:
            pass

        # Configure scaling for different DPI settings
        try:
            # Get system DPI scaling
            import tkinter as tk
            temp_root = tk.Tk()
            dpi = temp_root.winfo_fpixels('1i')
            temp_root.destroy()

            # Calculate scaling factor (96 DPI = 100% scaling)
            self.dpi_scale = dpi / 96.0
            self.logger.info(f"Detected DPI scaling: {self.dpi_scale:.2f}x")

            # Adjust font sizes for DPI scaling
            self.design_system['typography']['base_size'] = int(14 * self.dpi_scale)
            self.design_system['typography']['heading_size'] = int(24 * self.dpi_scale)
            self.design_system['typography']['subheading_size'] = int(18 * self.dpi_scale)

        except Exception as e:
            self.logger.warning(f"Could not detect DPI scaling: {e}")
            self.dpi_scale = 1.0

        # Configure ultra-professional window
        self.root.configure(fg_color=self.design_system['colors']['bg_primary'])

        # Set window properties for professional appearance
        self.root.resizable(True, True)

        # Try to set window icon with high-resolution support
        try:
            # Try high-res icon first
            icon_path = "assets/gideon_enterprise_icon_hd.ico"
            if not os.path.exists(icon_path):
                icon_path = "assets/gideon_enterprise_icon.ico"
            self.root.iconbitmap(icon_path)
        except:
            pass

        # Set window attributes for professional look
        try:
            self.root.attributes('-alpha', 0.98)  # Slight transparency for modern look
            # Enable smooth rendering
            self.root.attributes('-smoothresize', True)
        except:
            pass

        # Configure for crisp rendering on high-DPI displays
        try:
            self.root.tk.call('tk', 'scaling', self.dpi_scale)
        except:
            pass
    
    def _setup_fallback_window(self):
        """Setup fallback window for standard Tkinter"""
        self.root.title("🤖 GIDEON AI Assistant - Enterprise Edition")
        self.root.geometry("1600x1000")
        self.root.minsize(1200, 800)
        self.root.configure(bg=self.design_system['colors']['bg_primary'])
    
    def _create_ultra_professional_layout(self):
        """Create ultra-professional layout with advanced design patterns"""
        if CUSTOMTKINTER_AVAILABLE:
            self._create_enterprise_layout()
        else:
            self._create_fallback_layout()
    
    def _create_enterprise_layout(self):
        """Create enterprise-grade layout with advanced components"""
        # Main container with professional spacing
        self.main_container = ctk.CTkFrame(
            self.root, 
            fg_color="transparent",
            corner_radius=0
        )
        self.main_container.pack(
            fill="both", 
            expand=True, 
            padx=self.design_system['spacing']['lg'], 
            pady=self.design_system['spacing']['lg']
        )
        
        # Configure advanced grid system
        self.main_container.grid_columnconfigure(1, weight=1)
        self.main_container.grid_rowconfigure(1, weight=1)
        
        # Create enterprise components
        self._create_enterprise_header()
        self._create_enterprise_sidebar()
        self._create_enterprise_main_content()
        self._create_enterprise_status_bar()
        self._create_floating_panels()
    
    def _create_enterprise_header(self):
        """Create enterprise-grade header with branding and status"""
        header = ctk.CTkFrame(
            self.main_container,
            height=80,
            fg_color=self.design_system['colors']['bg_secondary'],
            corner_radius=self.design_system['radius']['lg']
        )
        header.grid(
            row=0, 
            column=0, 
            columnspan=2, 
            sticky="ew", 
            pady=(0, self.design_system['spacing']['lg'])
        )
        header.grid_propagate(False)
        header.grid_columnconfigure(1, weight=1)
        
        # Brand section
        brand_frame = ctk.CTkFrame(header, fg_color="transparent")
        brand_frame.grid(row=0, column=0, sticky="w", padx=self.design_system['spacing']['xl'])
        
        # Logo and title
        logo_label = ctk.CTkLabel(
            brand_frame,
            text="🤖",
            font=ctk.CTkFont(size=32),
            text_color=self.design_system['colors']['accent_primary']
        )
        logo_label.pack(side="left", padx=(0, self.design_system['spacing']['md']))
        
        title_frame = ctk.CTkFrame(brand_frame, fg_color="transparent")
        title_frame.pack(side="left")
        
        title_label = ctk.CTkLabel(
            title_frame,
            text="GIDEON AI",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=self.design_system['colors']['text_primary']
        )
        title_label.pack(anchor="w")
        
        subtitle_label = ctk.CTkLabel(
            title_frame,
            text="Enterprise AI Assistant",
            font=ctk.CTkFont(size=12),
            text_color=self.design_system['colors']['text_secondary']
        )
        subtitle_label.pack(anchor="w")
        
        # Status indicators in header
        status_frame = ctk.CTkFrame(header, fg_color="transparent")
        status_frame.grid(row=0, column=2, sticky="e", padx=self.design_system['spacing']['xl'])
        
        # Real-time clock
        self.clock_label = ctk.CTkLabel(
            status_frame,
            text="",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.design_system['colors']['text_primary']
        )
        self.clock_label.pack(side="right", padx=self.design_system['spacing']['md'])
        
        # Connection status
        self.connection_status = ctk.CTkLabel(
            status_frame,
            text="● ONLINE",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=self.design_system['colors']['accent_success']
        )
        self.connection_status.pack(side="right", padx=self.design_system['spacing']['md'])
    
    def _create_enterprise_sidebar(self):
        """Create enterprise-grade sidebar with advanced navigation and scroll capability"""
        self.sidebar = ctk.CTkFrame(
            self.main_container,
            width=320,
            fg_color=self.design_system['colors']['bg_secondary'],
            corner_radius=self.design_system['radius']['lg']
        )
        self.sidebar.grid(
            row=1,
            column=0,
            sticky="nsew",
            padx=(0, self.design_system['spacing']['lg'])
        )
        self.sidebar.grid_propagate(False)

        # Create scrollable frame for sidebar content
        self.sidebar_scrollable = ctk.CTkScrollableFrame(
            self.sidebar,
            fg_color="transparent",
            scrollbar_button_color=self.design_system['colors']['accent_primary'],
            scrollbar_button_hover_color=self.design_system['colors']['accent_secondary']
        )
        self.sidebar_scrollable.pack(fill="both", expand=True, padx=5, pady=5)
        
        # AI Status Dashboard
        self._create_ai_status_dashboard()

        # Performance Monitor
        self._create_performance_monitor()

        # Navigation Menu
        self._create_navigation_menu()

        # Thinking Animation Panel
        self._create_thinking_panel()

        # Terminal Interface Panel
        self._create_terminal_panel()

        # Quick Actions
        self._create_quick_actions_panel()
    
    def _create_ai_status_dashboard(self):
        """Create comprehensive AI status dashboard"""
        dashboard = ctk.CTkFrame(
            self.sidebar_scrollable,
            fg_color=self.design_system['colors']['bg_tertiary'],
            corner_radius=self.design_system['radius']['md']
        )
        dashboard.pack(
            fill="x",
            padx=self.design_system['spacing']['md'],
            pady=(self.design_system['spacing']['md'], self.design_system['spacing']['sm'])
        )
        
        # Dashboard header
        header = ctk.CTkLabel(
            dashboard,
            text="🧠 AI STATUS DASHBOARD",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.design_system['colors']['text_primary']
        )
        header.pack(
            anchor="w", 
            padx=self.design_system['spacing']['lg'], 
            pady=(self.design_system['spacing']['lg'], self.design_system['spacing']['sm'])
        )
        
        # Model status with progress indicator
        model_frame = ctk.CTkFrame(dashboard, fg_color="transparent")
        model_frame.pack(
            fill="x", 
            padx=self.design_system['spacing']['lg'], 
            pady=self.design_system['spacing']['sm']
        )
        
        self.model_status_label = ctk.CTkLabel(
            model_frame,
            text="🤖 Model: Loading...",
            font=ctk.CTkFont(size=12),
            text_color=self.design_system['colors']['text_secondary']
        )
        self.model_status_label.pack(anchor="w")
        
        self.model_progress = ctk.CTkProgressBar(
            model_frame,
            height=4,
            fg_color=self.design_system['colors']['bg_elevated'],
            progress_color=self.design_system['colors']['accent_primary']
        )
        self.model_progress.pack(fill="x", pady=(4, 0))
        self.model_progress.set(0.0)
        
        # Voice status
        voice_frame = ctk.CTkFrame(dashboard, fg_color="transparent")
        voice_frame.pack(
            fill="x", 
            padx=self.design_system['spacing']['lg'], 
            pady=self.design_system['spacing']['sm']
        )
        
        self.voice_status_label = ctk.CTkLabel(
            voice_frame,
            text="🎤 Voice: Initializing...",
            font=ctk.CTkFont(size=12),
            text_color=self.design_system['colors']['text_secondary']
        )
        self.voice_status_label.pack(anchor="w")
        
        # Memory status
        memory_frame = ctk.CTkFrame(dashboard, fg_color="transparent")
        memory_frame.pack(
            fill="x", 
            padx=self.design_system['spacing']['lg'], 
            pady=(self.design_system['spacing']['sm'], self.design_system['spacing']['lg'])
        )
        
        self.memory_status_label = ctk.CTkLabel(
            memory_frame,
            text="🧠 Memory: Active",
            font=ctk.CTkFont(size=12),
            text_color=self.design_system['colors']['accent_success']
        )
        self.memory_status_label.pack(anchor="w")
    
    def _create_performance_monitor(self):
        """Create real-time performance monitoring panel"""
        monitor = ctk.CTkFrame(
            self.sidebar_scrollable,
            fg_color=self.design_system['colors']['bg_tertiary'],
            corner_radius=self.design_system['radius']['md']
        )
        monitor.pack(
            fill="x",
            padx=self.design_system['spacing']['md'],
            pady=self.design_system['spacing']['sm']
        )
        
        # Monitor header
        header = ctk.CTkLabel(
            monitor,
            text="📊 PERFORMANCE MONITOR",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.design_system['colors']['text_primary']
        )
        header.pack(
            anchor="w", 
            padx=self.design_system['spacing']['lg'], 
            pady=(self.design_system['spacing']['lg'], self.design_system['spacing']['sm'])
        )
        
        # Performance metrics
        metrics_frame = ctk.CTkFrame(monitor, fg_color="transparent")
        metrics_frame.pack(
            fill="x", 
            padx=self.design_system['spacing']['lg'], 
            pady=(0, self.design_system['spacing']['lg'])
        )
        
        # CPU Usage
        cpu_frame = ctk.CTkFrame(metrics_frame, fg_color="transparent")
        cpu_frame.pack(fill="x", pady=2)
        
        self.cpu_label = ctk.CTkLabel(
            cpu_frame,
            text="CPU: 0%",
            font=ctk.CTkFont(size=11),
            text_color=self.design_system['colors']['text_secondary']
        )
        self.cpu_label.pack(side="left")
        
        self.cpu_progress = ctk.CTkProgressBar(
            cpu_frame,
            height=6,
            width=120,
            fg_color=self.design_system['colors']['bg_elevated'],
            progress_color=self.design_system['colors']['accent_cyan']
        )
        self.cpu_progress.pack(side="right")
        self.cpu_progress.set(0.0)
        
        # Memory Usage
        memory_frame = ctk.CTkFrame(metrics_frame, fg_color="transparent")
        memory_frame.pack(fill="x", pady=2)
        
        self.memory_label = ctk.CTkLabel(
            memory_frame,
            text="RAM: 0%",
            font=ctk.CTkFont(size=11),
            text_color=self.design_system['colors']['text_secondary']
        )
        self.memory_label.pack(side="left")
        
        self.memory_progress = ctk.CTkProgressBar(
            memory_frame,
            height=6,
            width=120,
            fg_color=self.design_system['colors']['bg_elevated'],
            progress_color=self.design_system['colors']['accent_warning']
        )
        self.memory_progress.pack(side="right")
        self.memory_progress.set(0.0)
        
        # Response Time
        response_frame = ctk.CTkFrame(metrics_frame, fg_color="transparent")
        response_frame.pack(fill="x", pady=2)
        
        self.response_label = ctk.CTkLabel(
            response_frame,
            text="Response: 0ms",
            font=ctk.CTkFont(size=11),
            text_color=self.design_system['colors']['text_secondary']
        )
        self.response_label.pack(side="left")
        
        self.response_indicator = ctk.CTkLabel(
            response_frame,
            text="●",
            font=ctk.CTkFont(size=16),
            text_color=self.design_system['colors']['accent_success']
        )
        self.response_indicator.pack(side="right")

    def _create_navigation_menu(self):
        """Create professional navigation menu with hover effects"""
        nav_container = ctk.CTkFrame(
            self.sidebar_scrollable,
            fg_color=self.design_system['colors']['bg_tertiary'],
            corner_radius=self.design_system['radius']['md']
        )
        nav_container.pack(
            fill="x",
            padx=self.design_system['spacing']['md'],
            pady=self.design_system['spacing']['sm']
        )

        # Navigation header
        nav_header = ctk.CTkLabel(
            nav_container,
            text="🧭 NAVIGATION",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.design_system['colors']['text_primary']
        )
        nav_header.pack(
            anchor="w",
            padx=self.design_system['spacing']['lg'],
            pady=(self.design_system['spacing']['lg'], self.design_system['spacing']['sm'])
        )

        # Navigation buttons with professional styling
        nav_buttons = [
            {
                'text': '💬 Conversation',
                'command': self._focus_chat,
                'color': self.design_system['colors']['accent_primary'],
                'description': 'Chat with Gideon AI'
            },
            {
                'text': '🤖 AI Models',
                'command': self._open_model_manager,
                'color': self.design_system['colors']['accent_purple'],
                'description': 'Manage AI models'
            },
            {
                'text': '🎤 Voice Control',
                'command': self._test_speech,
                'color': self.design_system['colors']['accent_secondary'],
                'description': 'Voice settings & test'
            },
            {
                'text': '📊 Analytics',
                'command': self._open_analytics,
                'color': self.design_system['colors']['accent_cyan'],
                'description': 'Performance analytics'
            },
            {
                'text': '📸 Screenshot',
                'command': self._take_screenshot,
                'color': self.design_system['colors']['accent_warning'],
                'description': 'Capture screen'
            },
            {
                'text': '⚙️ Settings',
                'command': self._open_settings,
                'color': self.design_system['colors']['text_secondary'],
                'description': 'System configuration'
            },
            {
                'text': '💬 Compact Chat',
                'command': self._show_compact_chat_manual,
                'color': self.design_system['colors']['accent_cyan'],
                'description': 'Show compact chat window'
            }
        ]

        for btn_config in nav_buttons:
            btn_frame = ctk.CTkFrame(nav_container, fg_color="transparent")
            btn_frame.pack(
                fill="x",
                padx=self.design_system['spacing']['lg'],
                pady=2
            )

            btn = ctk.CTkButton(
                btn_frame,
                text=btn_config['text'],
                command=btn_config['command'],
                fg_color=self.design_system['colors']['bg_elevated'],
                hover_color=self.design_system['colors']['hover_bg'],
                text_color=btn_config['color'],
                font=ctk.CTkFont(size=12, weight="bold"),
                height=45,
                anchor="w",
                corner_radius=self.design_system['radius']['md']
            )
            btn.pack(fill="x")

            # Add tooltip-like description
            desc_label = ctk.CTkLabel(
                btn_frame,
                text=btn_config['description'],
                font=ctk.CTkFont(size=9),
                text_color=self.design_system['colors']['text_muted']
            )
            desc_label.pack(anchor="w", padx=self.design_system['spacing']['md'])

        # Add spacing
        ctk.CTkLabel(nav_container, text="", height=self.design_system['spacing']['md']).pack()

    def _create_thinking_panel(self):
        """Create thinking animation panel for when Gideon is processing"""
        self.thinking_container = ctk.CTkFrame(
            self.sidebar_scrollable,
            fg_color=self.design_system['colors']['bg_tertiary'],
            corner_radius=self.design_system['radius']['md']
        )
        self.thinking_container.pack(
            fill="x",
            padx=self.design_system['spacing']['md'],
            pady=self.design_system['spacing']['sm']
        )

        # Thinking header
        thinking_header = ctk.CTkLabel(
            self.thinking_container,
            text="🧠 GIDEON'S THOUGHTS",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.design_system['colors']['text_primary']
        )
        thinking_header.pack(
            anchor="w",
            padx=self.design_system['spacing']['lg'],
            pady=(self.design_system['spacing']['lg'], self.design_system['spacing']['sm'])
        )

        # Thinking status
        self.thinking_status = ctk.CTkLabel(
            self.thinking_container,
            text="💭 Ready to assist",
            font=ctk.CTkFont(size=12),
            text_color=self.design_system['colors']['text_secondary']
        )
        self.thinking_status.pack(
            anchor="w",
            padx=self.design_system['spacing']['lg'],
            pady=self.design_system['spacing']['sm']
        )

        # Thinking animation dots
        self.thinking_dots_frame = ctk.CTkFrame(self.thinking_container, fg_color="transparent")
        self.thinking_dots_frame.pack(
            anchor="w",
            padx=self.design_system['spacing']['lg'],
            pady=self.design_system['spacing']['sm']
        )

        # Create animated thinking dots
        self.thinking_dots = []
        for i in range(3):
            dot = ctk.CTkLabel(
                self.thinking_dots_frame,
                text="●",
                font=ctk.CTkFont(size=16),
                text_color=self.design_system['colors']['accent_primary']
            )
            dot.pack(side="left", padx=2)
            self.thinking_dots.append(dot)

        # Thinking progress bar
        self.thinking_progress = ctk.CTkProgressBar(
            self.thinking_container,
            height=6,
            fg_color=self.design_system['colors']['bg_elevated'],
            progress_color=self.design_system['colors']['accent_purple']
        )
        self.thinking_progress.pack(
            fill="x",
            padx=self.design_system['spacing']['lg'],
            pady=(self.design_system['spacing']['sm'], self.design_system['spacing']['lg'])
        )
        self.thinking_progress.set(0.0)

        # Keep thinking panel hidden permanently (no animations)
        self.thinking_container.pack_forget()

        # Thinking animation state
        self.thinking_active = False
        self.thinking_dot_index = 0

    def _create_quick_actions_panel(self):
        """Create quick actions panel with professional styling"""
        actions_container = ctk.CTkFrame(
            self.sidebar_scrollable,
            fg_color=self.design_system['colors']['bg_tertiary'],
            corner_radius=self.design_system['radius']['md']
        )
        actions_container.pack(
            fill="x",
            padx=self.design_system['spacing']['md'],
            pady=self.design_system['spacing']['sm']
        )

        # Actions header
        actions_header = ctk.CTkLabel(
            actions_container,
            text="⚡ QUICK ACTIONS",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.design_system['colors']['text_primary']
        )
        actions_header.pack(
            anchor="w",
            padx=self.design_system['spacing']['lg'],
            pady=(self.design_system['spacing']['lg'], self.design_system['spacing']['sm'])
        )

        # Action buttons grid
        actions_grid = ctk.CTkFrame(actions_container, fg_color="transparent")
        actions_grid.pack(
            fill="x",
            padx=self.design_system['spacing']['lg'],
            pady=(0, self.design_system['spacing']['lg'])
        )
        actions_grid.grid_columnconfigure(0, weight=1)
        actions_grid.grid_columnconfigure(1, weight=1)

        # Language toggle
        self.language_button = ctk.CTkButton(
            actions_grid,
            text="🌍 عربي/EN",
            command=self._toggle_language,
            fg_color=self.design_system['colors']['accent_success'],
            hover_color=self.design_system['colors']['accent_success'],
            font=ctk.CTkFont(size=11, weight="bold"),
            height=40,
            corner_radius=self.design_system['radius']['md']
        )
        self.language_button.grid(row=0, column=0, columnspan=2, sticky="ew", pady=(0, 8))

        # Clear chat
        clear_button = ctk.CTkButton(
            actions_grid,
            text="🗑️ Clear",
            command=self._clear_chat,
            fg_color=self.design_system['colors']['accent_error'],
            hover_color=self.design_system['colors']['accent_error'],
            font=ctk.CTkFont(size=11, weight="bold"),
            height=35,
            corner_radius=self.design_system['radius']['md']
        )
        clear_button.grid(row=1, column=0, sticky="ew", padx=(0, 4))

        # Emergency stop
        emergency_button = ctk.CTkButton(
            actions_grid,
            text="🛑 Stop",
            command=self._emergency_stop,
            fg_color=self.design_system['colors']['accent_error'],
            hover_color=self.design_system['colors']['accent_error'],
            font=ctk.CTkFont(size=11, weight="bold"),
            height=35,
            corner_radius=self.design_system['radius']['md']
        )
        emergency_button.grid(row=1, column=1, sticky="ew", padx=(4, 0))

    def _create_terminal_panel(self):
        """Create terminal access panel in sidebar"""
        terminal_panel = ctk.CTkFrame(
            self.sidebar_scrollable,
            fg_color=self.design_system['colors']['bg_tertiary'],
            corner_radius=self.design_system['radius']['md']
        )
        terminal_panel.pack(
            fill="x",
            padx=self.design_system['spacing']['md'],
            pady=self.design_system['spacing']['sm']
        )

        # Terminal panel header
        header = ctk.CTkLabel(
            terminal_panel,
            text="💻 TERMINAL ACCESS",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.design_system['colors']['text_primary']
        )
        header.pack(
            anchor="w",
            padx=self.design_system['spacing']['lg'],
            pady=(self.design_system['spacing']['lg'], self.design_system['spacing']['sm'])
        )

        # Terminal controls frame
        controls_frame = ctk.CTkFrame(terminal_panel, fg_color="transparent")
        controls_frame.pack(
            fill="x",
            padx=self.design_system['spacing']['lg'],
            pady=(0, self.design_system['spacing']['lg'])
        )
        controls_frame.grid_columnconfigure(0, weight=1)
        controls_frame.grid_columnconfigure(1, weight=1)

        # Open terminal button
        self.terminal_open_button = ctk.CTkButton(
            controls_frame,
            text="🖥️ Open",
            command=self._toggle_terminal,
            fg_color=self.design_system['colors']['accent_primary'],
            hover_color=self.design_system['colors']['accent_primary'],
            font=ctk.CTkFont(size=11, weight="bold"),
            height=35,
            corner_radius=self.design_system['radius']['md']
        )
        self.terminal_open_button.grid(row=0, column=0, sticky="ew", padx=(0, 4))

        # Terminal status button
        self.terminal_status_button = ctk.CTkButton(
            controls_frame,
            text="📊 Status",
            command=self._show_terminal_status,
            fg_color=self.design_system['colors']['bg_elevated'],
            hover_color=self.design_system['colors']['hover_bg'],
            text_color=self.design_system['colors']['text_secondary'],
            font=ctk.CTkFont(size=11, weight="bold"),
            height=35,
            corner_radius=self.design_system['radius']['md']
        )
        self.terminal_status_button.grid(row=0, column=1, sticky="ew", padx=(4, 0))

        # Quick commands frame
        quick_commands_frame = ctk.CTkFrame(terminal_panel, fg_color="transparent")
        quick_commands_frame.pack(
            fill="x",
            padx=self.design_system['spacing']['lg'],
            pady=(0, self.design_system['spacing']['lg'])
        )

        # Quick command label
        quick_label = ctk.CTkLabel(
            quick_commands_frame,
            text="Quick Commands:",
            font=ctk.CTkFont(size=10, weight="bold"),
            text_color=self.design_system['colors']['text_secondary']
        )
        quick_label.pack(anchor="w", pady=(0, 4))

        # Quick command buttons grid
        quick_grid = ctk.CTkFrame(quick_commands_frame, fg_color="transparent")
        quick_grid.pack(fill="x")
        quick_grid.grid_columnconfigure(0, weight=1)
        quick_grid.grid_columnconfigure(1, weight=1)

        # Dir/ls button
        dir_button = ctk.CTkButton(
            quick_grid,
            text="📁 Dir",
            command=lambda: self._execute_quick_command("dir" if self._is_windows() else "ls"),
            fg_color=self.design_system['colors']['accent_cyan'],
            hover_color=self.design_system['colors']['accent_cyan'],
            font=ctk.CTkFont(size=9, weight="bold"),
            height=25,
            corner_radius=self.design_system['radius']['sm']
        )
        dir_button.grid(row=0, column=0, sticky="ew", padx=(0, 2), pady=2)

        # System info button
        sysinfo_button = ctk.CTkButton(
            quick_grid,
            text="ℹ️ Info",
            command=lambda: self._execute_quick_command("systeminfo" if self._is_windows() else "uname -a"),
            fg_color=self.design_system['colors']['accent_purple'],
            hover_color=self.design_system['colors']['accent_purple'],
            font=ctk.CTkFont(size=9, weight="bold"),
            height=25,
            corner_radius=self.design_system['radius']['sm']
        )
        sysinfo_button.grid(row=0, column=1, sticky="ew", padx=(2, 0), pady=2)

        # Network button
        network_button = ctk.CTkButton(
            quick_grid,
            text="🌐 Network",
            command=lambda: self._execute_quick_command("ipconfig" if self._is_windows() else "ifconfig"),
            fg_color=self.design_system['colors']['accent_warning'],
            hover_color=self.design_system['colors']['accent_warning'],
            font=ctk.CTkFont(size=9, weight="bold"),
            height=25,
            corner_radius=self.design_system['radius']['sm']
        )
        network_button.grid(row=1, column=0, sticky="ew", padx=(0, 2), pady=2)

        # Processes button
        processes_button = ctk.CTkButton(
            quick_grid,
            text="⚙️ Tasks",
            command=lambda: self._execute_quick_command("tasklist" if self._is_windows() else "ps aux"),
            fg_color=self.design_system['colors']['accent_error'],
            hover_color=self.design_system['colors']['accent_error'],
            font=ctk.CTkFont(size=9, weight="bold"),
            height=25,
            corner_radius=self.design_system['radius']['sm']
        )
        processes_button.grid(row=1, column=1, sticky="ew", padx=(2, 0), pady=2)

        # Initialize terminal interface
        try:
            # Create a container for the terminal interface (initially hidden)
            self.terminal_container = ctk.CTkFrame(
                self.main_content,
                fg_color=self.design_system['colors']['bg_tertiary'],
                corner_radius=self.design_system['radius']['md']
            )

            # Initialize terminal interface
            self.terminal_interface = TerminalInterface(self.terminal_container, self.gideon_core)

            # Set up callbacks
            self.terminal_interface.on_command_executed = self._on_terminal_command_executed

            self.logger.info("Terminal interface initialized successfully")

        except Exception as e:
            self.logger.error(f"Error initializing terminal interface: {e}")
            # Create fallback message
            self._create_terminal_fallback()

    def _create_terminal_fallback(self):
        """Create fallback when terminal interface fails"""
        fallback_label = ctk.CTkLabel(
            self.sidebar_scrollable,
            text="⚠️ Terminal interface unavailable",
            font=ctk.CTkFont(size=10),
            text_color=self.design_system['colors']['accent_warning']
        )
        fallback_label.pack(
            padx=self.design_system['spacing']['lg'],
            pady=self.design_system['spacing']['sm']
        )

    def _toggle_terminal(self):
        """Toggle terminal interface visibility"""
        try:
            if self.terminal_interface:
                if hasattr(self.terminal_interface, 'is_visible') and self.terminal_interface.is_visible:
                    # Hide terminal
                    self.terminal_container.grid_forget()
                    self.terminal_interface.hide()
                    self.terminal_open_button.configure(text="🖥️ Open")
                    self._add_message("System", "Terminal interface closed", self.design_system['colors']['text_secondary'])
                else:
                    # Show terminal
                    self.terminal_container.grid(
                        row=5,
                        column=0,
                        sticky="nsew",
                        padx=self.design_system['spacing']['xl'],
                        pady=self.design_system['spacing']['md']
                    )
                    self.terminal_interface.show()
                    self.terminal_open_button.configure(text="🖥️ Close")
                    self._add_message("System", "Terminal interface opened", self.design_system['colors']['accent_success'])
            else:
                self._add_message("System", "Terminal interface not available", self.design_system['colors']['accent_error'])

        except Exception as e:
            self.logger.error(f"Error toggling terminal: {e}")
            self._add_message("System", f"Terminal error: {e}", self.design_system['colors']['accent_error'])

    def _show_terminal_status(self):
        """Show terminal status information"""
        try:
            if self.terminal_interface:
                status = self.terminal_interface.get_status()
                status_msg = f"Terminal Status:\n"
                status_msg += f"• Visible: {'Yes' if status.get('visible', False) else 'No'}\n"
                status_msg += f"• Available: {'Yes' if status.get('terminal_available', False) else 'No'}\n"
                status_msg += f"• Active Session: {status.get('active_session', 'None')}\n"
                status_msg += f"• Commands in History: {status.get('command_history_count', 0)}"

                self._add_message("System", status_msg, self.design_system['colors']['text_secondary'])
            else:
                self._add_message("System", "Terminal interface not initialized", self.design_system['colors']['accent_error'])

        except Exception as e:
            self.logger.error(f"Error showing terminal status: {e}")
            self._add_message("System", f"Error getting terminal status: {e}", self.design_system['colors']['accent_error'])

    def _execute_quick_command(self, command: str):
        """Execute a quick terminal command"""
        try:
            if self.terminal_interface:
                # Ensure terminal is visible
                if not (hasattr(self.terminal_interface, 'is_visible') and self.terminal_interface.is_visible):
                    self._toggle_terminal()

                # Execute command
                self.terminal_interface.execute_command_from_voice(command)
                self._add_message("System", f"Executing: {command}", self.design_system['colors']['accent_cyan'])
            else:
                self._add_message("System", "Terminal interface not available", self.design_system['colors']['accent_error'])

        except Exception as e:
            self.logger.error(f"Error executing quick command: {e}")
            self._add_message("System", f"Command execution error: {e}", self.design_system['colors']['accent_error'])

    def _is_windows(self) -> bool:
        """Check if running on Windows"""
        import platform
        return platform.system().lower() == 'windows'

    def _on_terminal_command_executed(self, command: str, result: dict):
        """Handle terminal command execution callback"""
        try:
            if result.get('success'):
                self._add_message("Terminal", f"✅ Command completed: {command}", self.design_system['colors']['accent_success'])
            else:
                error = result.get('error', 'Unknown error')
                self._add_message("Terminal", f"❌ Command failed: {command} - {error}", self.design_system['colors']['accent_error'])

        except Exception as e:
            self.logger.error(f"Error handling terminal callback: {e}")

    def _create_enterprise_main_content(self):
        """Create enterprise-grade main content area with avatar and voice chat"""
        self.main_content = ctk.CTkFrame(
            self.main_container,
            fg_color=self.design_system['colors']['bg_secondary'],
            corner_radius=self.design_system['radius']['lg']
        )
        self.main_content.grid(row=1, column=1, sticky="nsew")
        self.main_content.grid_rowconfigure(3, weight=1)  # Chat display gets most space
        self.main_content.grid_columnconfigure(0, weight=1)

        # Avatar completely removed - no visual avatar element

        # Voice Chat Interface (ChatGPT-style)
        self._create_voice_chat_interface()

        # Chat header with advanced features
        self._create_chat_header()

        # Chat display with professional styling
        self._create_chat_display()

        # Advanced input area
        self._create_advanced_input_area()



    def _create_voice_chat_interface(self):
        """Create ChatGPT-style voice chat interface"""
        try:
            voice_frame = ctk.CTkFrame(
                self.main_content,
                height=200,
                fg_color=self.design_system['colors']['bg_tertiary'],
                corner_radius=self.design_system['radius']['md']
            )
            voice_frame.grid(row=1, column=0, sticky="ew", pady=(0, 10))
            voice_frame.grid_propagate(False)

            # Initialize voice chat interface
            self.voice_chat_interface = VoiceChatInterface(voice_frame, self.gideon_core)

            # Set up callbacks
            self.voice_chat_interface.on_voice_start = self._on_voice_chat_start
            self.voice_chat_interface.on_voice_end = self._on_voice_chat_end

        except Exception as e:
            self.logger.error(f"Error creating voice chat interface: {e}")
            # Create fallback voice interface
            self._create_fallback_voice_interface()

    def _create_fallback_voice_interface(self):
        """Create fallback voice interface"""
        fallback_frame = ctk.CTkFrame(
            self.main_content,
            height=80,
            fg_color=self.design_system['colors']['bg_tertiary']
        )
        fallback_frame.grid(row=1, column=0, sticky="ew", pady=(0, 10))
        fallback_frame.grid_propagate(False)

        # Simple voice controls
        voice_label = ctk.CTkLabel(
            fallback_frame,
            text="🎤 Voice Chat (Fallback Mode)",
            font=ctk.CTkFont(size=14),
            text_color=self.design_system['colors']['text_secondary']
        )
        voice_label.pack(expand=True)

    def _create_chat_header(self):
        """Create professional chat header with status and controls"""
        chat_header = ctk.CTkFrame(
            self.main_content,
            height=70,
            fg_color=self.design_system['colors']['bg_tertiary'],
            corner_radius=self.design_system['radius']['md']
        )
        chat_header.grid(
            row=2,
            column=0,
            sticky="ew",
            padx=self.design_system['spacing']['xl'],
            pady=(self.design_system['spacing']['xl'], self.design_system['spacing']['md'])
        )
        chat_header.grid_propagate(False)
        chat_header.grid_columnconfigure(1, weight=1)

        # Chat title and status
        title_frame = ctk.CTkFrame(chat_header, fg_color="transparent")
        title_frame.grid(row=0, column=0, sticky="w", padx=self.design_system['spacing']['xl'])

        chat_title = ctk.CTkLabel(
            title_frame,
            text="💬 Conversation with Gideon",
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color=self.design_system['colors']['text_primary']
        )
        chat_title.pack(anchor="w")

        # AI status with animated indicator
        status_frame = ctk.CTkFrame(title_frame, fg_color="transparent")
        status_frame.pack(anchor="w", pady=(4, 0))

        self.chat_status_indicator = ctk.CTkLabel(
            status_frame,
            text="●",
            font=ctk.CTkFont(size=12),
            text_color=self.design_system['colors']['accent_success']
        )
        self.chat_status_indicator.pack(side="left")

        self.chat_status_label = ctk.CTkLabel(
            status_frame,
            text="AI Online - Ready to assist",
            font=ctk.CTkFont(size=12),
            text_color=self.design_system['colors']['text_secondary']
        )
        self.chat_status_label.pack(side="left", padx=(4, 0))

        # Chat controls
        controls_frame = ctk.CTkFrame(chat_header, fg_color="transparent")
        controls_frame.grid(row=0, column=2, sticky="e", padx=self.design_system['spacing']['xl'])

        # Export chat button
        export_button = ctk.CTkButton(
            controls_frame,
            text="📤 Export",
            command=self._export_chat,
            fg_color=self.design_system['colors']['bg_elevated'],
            hover_color=self.design_system['colors']['hover_bg'],
            text_color=self.design_system['colors']['text_secondary'],
            font=ctk.CTkFont(size=11),
            width=80,
            height=30,
            corner_radius=self.design_system['radius']['md']
        )
        export_button.pack(side="right", padx=(8, 0))

        # Minimize chat button
        minimize_button = ctk.CTkButton(
            controls_frame,
            text="📋 History",
            command=self._show_chat_history,
            fg_color=self.design_system['colors']['bg_elevated'],
            hover_color=self.design_system['colors']['hover_bg'],
            text_color=self.design_system['colors']['text_secondary'],
            font=ctk.CTkFont(size=11),
            width=80,
            height=30,
            corner_radius=self.design_system['radius']['md']
        )
        minimize_button.pack(side="right")

    def _create_chat_display(self):
        """Create professional chat display with advanced features"""
        chat_container = ctk.CTkFrame(
            self.main_content,
            fg_color="transparent"
        )
        chat_container.grid(
            row=3,
            column=0,
            sticky="nsew",
            padx=self.design_system['spacing']['xl'],
            pady=(0, self.design_system['spacing']['md'])
        )
        chat_container.grid_rowconfigure(0, weight=1)
        chat_container.grid_columnconfigure(0, weight=1)

        # Chat display with professional styling - PERMANENTLY VISIBLE
        self.chat_display = ctk.CTkTextbox(
            chat_container,
            font=ctk.CTkFont(family="Consolas", size=13),
            fg_color=self.design_system['colors']['bg_primary'],
            text_color=self.design_system['colors']['text_primary'],
            wrap="word",
            corner_radius=self.design_system['radius']['md'],
            border_width=1,
            border_color=self.design_system['colors']['border_default'],
            state="normal"  # ALWAYS NORMAL - never disabled, always visible
        )
        self.chat_display.grid(row=0, column=0, sticky="nsew")

        # ENSURE CHAT DISPLAY IS ALWAYS VISIBLE AND ACCESSIBLE
        self.chat_display.grid_propagate(True)  # Allow proper sizing
        chat_container.grid_propagate(True)     # Allow container to size properly

        # Test the chat display immediately
        try:
            self.chat_display.insert("0.0", "🔧 Chat display initialized...\n")
            self.logger.info("✅ Chat display widget created successfully")
        except Exception as e:
            self.logger.error(f"Chat display initialization failed: {e}")

        # Store reference for debugging
        self._chat_widget_type = "CustomTkinter" if CUSTOMTKINTER_AVAILABLE else "Standard"
        self.logger.info(f"Chat widget type: {self._chat_widget_type}")

        # Add scrollbar styling (if possible)
        try:
            self.chat_display.configure(scrollbar_button_color=self.design_system['colors']['accent_primary'])
        except:
            pass

    def _create_advanced_input_area(self):
        """Create advanced input area with professional features"""
        input_container = ctk.CTkFrame(
            self.main_content,
            height=120,
            fg_color="transparent"
        )
        input_container.grid(
            row=4,
            column=0,
            sticky="ew",
            padx=self.design_system['spacing']['xl'],
            pady=(0, self.design_system['spacing']['xl'])
        )
        input_container.grid_propagate(False)
        input_container.grid_columnconfigure(0, weight=1)

        # Input frame with professional styling
        input_frame = ctk.CTkFrame(
            input_container,
            fg_color=self.design_system['colors']['bg_tertiary'],
            corner_radius=self.design_system['radius']['xl'],
            border_width=2,
            border_color=self.design_system['colors']['border_default']
        )
        input_frame.grid(row=0, column=0, sticky="ew", pady=self.design_system['spacing']['md'])
        input_frame.grid_columnconfigure(0, weight=1)

        # Input entry with placeholder and styling
        self.input_entry = ctk.CTkEntry(
            input_frame,
            placeholder_text="Type your message to Gideon AI Assistant...",
            font=ctk.CTkFont(size=14),
            fg_color="transparent",
            border_width=0,
            height=60,
            placeholder_text_color=self.design_system['colors']['text_muted']
        )
        self.input_entry.grid(
            row=0,
            column=0,
            sticky="ew",
            padx=self.design_system['spacing']['xl'],
            pady=self.design_system['spacing']['lg']
        )

        # Button container
        button_container = ctk.CTkFrame(input_frame, fg_color="transparent")
        button_container.grid(
            row=0,
            column=1,
            padx=(0, self.design_system['spacing']['lg']),
            pady=self.design_system['spacing']['lg']
        )

        # Voice button with animation
        self.voice_button = ctk.CTkButton(
            button_container,
            text="🎤",
            width=60,
            height=60,
            font=ctk.CTkFont(size=24),
            command=self._toggle_voice_input,
            fg_color=self.design_system['colors']['accent_primary'],
            hover_color=self.design_system['colors']['accent_primary'],
            corner_radius=self.design_system['radius']['full']
        )
        self.voice_button.pack(side="right", padx=(8, 0))

        # Send button with professional styling
        # Create send button with explicit command binding
        def handle_send_click():
            self.logger.info("🖱️ Send button clicked!")
            self._send_message()

        self.send_button = ctk.CTkButton(
            button_container,
            text="Send",
            width=80,
            height=60,
            font=ctk.CTkFont(size=14, weight="bold"),
            command=handle_send_click,
            fg_color=self.design_system['colors']['accent_success'],
            hover_color=self.design_system['colors']['accent_success'],
            corner_radius=self.design_system['radius']['xl']
        )
        self.send_button.pack(side="right")
        self.logger.info("✅ Send button created with explicit command binding")

        # Input status bar
        input_status = ctk.CTkFrame(input_container, fg_color="transparent", height=20)
        input_status.grid(row=1, column=0, sticky="ew")

        self.typing_indicator = ctk.CTkLabel(
            input_status,
            text="",
            font=ctk.CTkFont(size=10),
            text_color=self.design_system['colors']['text_muted']
        )
        self.typing_indicator.pack(side="left")

        self.char_counter = ctk.CTkLabel(
            input_status,
            text="0 characters",
            font=ctk.CTkFont(size=10),
            text_color=self.design_system['colors']['text_muted']
        )
        self.char_counter.pack(side="right")

    def _create_enterprise_status_bar(self):
        """Create enterprise-grade status bar with real-time information"""
        self.status_bar = ctk.CTkFrame(
            self.main_container,
            height=50,
            fg_color=self.design_system['colors']['bg_tertiary'],
            corner_radius=self.design_system['radius']['md']
        )
        self.status_bar.grid(
            row=2,
            column=0,
            columnspan=2,
            sticky="ew",
            pady=(self.design_system['spacing']['lg'], 0)
        )
        self.status_bar.grid_propagate(False)
        self.status_bar.grid_columnconfigure(1, weight=1)

        # Status information
        status_info = ctk.CTkFrame(self.status_bar, fg_color="transparent")
        status_info.grid(row=0, column=0, sticky="w", padx=self.design_system['spacing']['xl'])

        self.main_status_label = ctk.CTkLabel(
            status_info,
            text="✅ System Ready",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=self.design_system['colors']['accent_success']
        )
        self.main_status_label.pack(side="left")

        # Separator
        separator = ctk.CTkLabel(
            status_info,
            text="•",
            font=ctk.CTkFont(size=12),
            text_color=self.design_system['colors']['text_muted']
        )
        separator.pack(side="left", padx=self.design_system['spacing']['md'])

        # Performance indicator
        self.performance_label = ctk.CTkLabel(
            status_info,
            text="Performance: Optimal",
            font=ctk.CTkFont(size=11),
            text_color=self.design_system['colors']['text_secondary']
        )
        self.performance_label.pack(side="left")

        # Right side information
        right_info = ctk.CTkFrame(self.status_bar, fg_color="transparent")
        right_info.grid(row=0, column=2, sticky="e", padx=self.design_system['spacing']['xl'])

        # Version info
        version_label = ctk.CTkLabel(
            right_info,
            text="Gideon AI Enterprise v2.0",
            font=ctk.CTkFont(size=10),
            text_color=self.design_system['colors']['text_muted']
        )
        version_label.pack(side="right")

    def _create_floating_panels(self):
        """Create floating panels for advanced features"""
        # This would create floating panels for notifications, etc.
        # Implementation would depend on specific requirements
        pass

    def _create_fallback_layout(self):
        """Create fallback layout for standard Tkinter"""
        # Simplified layout for systems without CustomTkinter
        main_frame = tk.Frame(self.root, bg=self.design_system['colors']['bg_primary'])
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Header
        header_frame = tk.Frame(main_frame, bg=self.design_system['colors']['bg_secondary'], height=80)
        header_frame.pack(fill="x", pady=(0, 20))
        header_frame.pack_propagate(False)

        title_label = tk.Label(
            header_frame,
            text="🤖 GIDEON AI Assistant - Enterprise Edition",
            font=("Arial", 20, "bold"),
            fg=self.design_system['colors']['accent_primary'],
            bg=self.design_system['colors']['bg_secondary']
        )
        title_label.pack(side="left", padx=30, pady=20)

        # Chat area
        self.chat_display = scrolledtext.ScrolledText(
            main_frame,
            font=("Consolas", 12),
            bg=self.design_system['colors']['bg_secondary'],
            fg=self.design_system['colors']['text_primary'],
            insertbackground=self.design_system['colors']['text_primary'],
            wrap=tk.WORD,
            height=30
        )
        self.chat_display.pack(fill="both", expand=True, pady=(0, 20))

        # Input area
        input_frame = tk.Frame(main_frame, bg=self.design_system['colors']['bg_primary'])
        input_frame.pack(fill="x")

        self.input_entry = tk.Entry(
            input_frame,
            font=("Arial", 14),
            bg=self.design_system['colors']['bg_tertiary'],
            fg=self.design_system['colors']['text_primary'],
            insertbackground=self.design_system['colors']['text_primary'],
            relief="flat",
            bd=10
        )
        self.input_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))

        self.voice_button = tk.Button(
            input_frame,
            text="🎤",
            font=("Arial", 16),
            bg=self.design_system['colors']['accent_primary'],
            fg=self.design_system['colors']['text_primary'],
            command=self._toggle_voice_input,
            relief="flat",
            width=4
        )
        self.voice_button.pack(side="right", padx=(0, 10))

        self.send_button = tk.Button(
            input_frame,
            text="Send",
            font=("Arial", 12, "bold"),
            bg=self.design_system['colors']['accent_success'],
            fg=self.design_system['colors']['text_inverse'],
            command=self._send_message,
            relief="flat",
            width=10
        )
        self.send_button.pack(side="right")

    def _setup_advanced_bindings(self):
        """Setup advanced event bindings and shortcuts"""
        try:
            # Enter key to send message - CustomTkinter compatible
            self.logger.info("🔗 Setting up Enter key binding for CustomTkinter...")

            # For CustomTkinter, we need to bind to the underlying tkinter widget
            def handle_enter(event):
                self.logger.info("⌨️ Enter key pressed!")
                self._send_message()
                return 'break'  # Prevent default behavior

            # CustomTkinter Entry widgets have an internal tkinter Entry widget
            if hasattr(self.input_entry, '_entry'):
                # Bind to the internal tkinter entry widget
                self.input_entry._entry.bind('<Return>', handle_enter)
                self.logger.info("✅ Enter key bound to CustomTkinter internal widget")
            else:
                # Fallback to standard binding
                self.input_entry.bind('<Return>', handle_enter)
                self.logger.info("✅ Enter key bound using standard method")

            # Character counter update - also bind to internal widget if available
            if hasattr(self.input_entry, '_entry'):
                self.input_entry._entry.bind('<KeyRelease>', self._update_char_counter)
            else:
                self.input_entry.bind('<KeyRelease>', self._update_char_counter)

            # Additional key bindings
            if hasattr(self.input_entry, '_entry'):
                self.input_entry._entry.bind('<Control-Return>', lambda e: self._send_message())
            else:
                self.input_entry.bind('<Control-Return>', lambda e: self._send_message())

            # Focus the input entry
            self.input_entry.focus_set()
            self.logger.info("✅ Input entry focused")

            # Test the binding by checking if we can access the internal widget
            if hasattr(self.input_entry, '_entry'):
                self.logger.info("✅ CustomTkinter internal widget binding successful")
            else:
                self.logger.info("✅ Standard widget binding successful")

        except Exception as e:
            self.logger.error(f"❌ Error setting up bindings: {e}")
            import traceback
            traceback.print_exc()

        # Focus events
        self.input_entry.bind('<FocusIn>', self._on_input_focus)
        self.input_entry.bind('<FocusOut>', self._on_input_blur)

        # Window close event
        self.root.protocol("WM_DELETE_WINDOW", self._on_window_close)

        # Keyboard shortcuts
        self.root.bind('<Control-n>', lambda e: self._clear_chat())
        self.root.bind('<Control-s>', lambda e: self._export_chat())
        self.root.bind('<Control-m>', lambda e: self._open_model_manager())
        self.root.bind('<F1>', lambda e: self._show_help())

        # Focus on input entry
        self.input_entry.focus_set()

    def _start_animations(self):
        """Animations disabled - chat box remains permanently visible"""
        try:
            # ANIMATIONS COMPLETELY DISABLED FOR PERMANENT CHAT BOX VISIBILITY
            self.animation_running = False

            # Only keep essential non-visual updates (no animations)
            self._update_real_time_clock_static()
            self._monitor_performance_static()

            self.logger.info("✅ Static interface initialized - chat box permanently visible")
        except Exception as e:
            self.logger.error(f"Error initializing static interface: {e}")
            self.animation_running = False

    def _animate_status_indicators(self):
        """Animate status indicators with professional effects (DISABLED - no animations)"""
        # ANIMATION DISABLED - Status indicator animations removed to ensure static interface
        return

    def _update_real_time_clock(self):
        """Update real-time clock in header (DISABLED - no animations)"""
        # ANIMATION DISABLED - Clock updates removed to ensure static interface
        return

    def _update_real_time_clock_static(self):
        """Set static clock display (no continuous updates)"""
        try:
            current_time = datetime.now().strftime("%H:%M:%S")
            current_date = datetime.now().strftime("%Y-%m-%d")

            if hasattr(self, 'clock_label'):
                self.clock_label.configure(text=f"{current_date} {current_time}")

            self.logger.debug("Static clock set")
        except Exception as e:
            self.logger.error(f"Static clock error: {e}")

    def _monitor_performance(self):
        """Monitor and update performance metrics (DISABLED - no animations)"""
        # ANIMATION DISABLED - Performance monitoring removed to ensure static interface
        return

    def _monitor_performance_static(self):
        """Set static performance display (no continuous updates)"""
        try:
            # Set static performance data
            self.performance_data = {
                'cpu_usage': 15.0,
                'memory_usage': 45.0,
                'response_time': 85.0,
                'model_load': 0,
                'start_time': time.time()
            }

            # Update UI elements once with static values
            if hasattr(self, 'cpu_progress'):
                self.cpu_progress.set(self.performance_data['cpu_usage'] / 100)
                self.cpu_label.configure(text=f"CPU: {self.performance_data['cpu_usage']:.1f}%")

            if hasattr(self, 'memory_progress'):
                self.memory_progress.set(self.performance_data['memory_usage'] / 100)
                self.memory_label.configure(text=f"RAM: {self.performance_data['memory_usage']:.1f}%")

            if hasattr(self, 'response_label'):
                self.response_label.configure(text=f"Response: {self.performance_data['response_time']:.0f}ms")

            # Set static performance status
            if hasattr(self, 'performance_label'):
                status = "Optimal"
                color = self.design_system['colors']['accent_success']
                self.performance_label.configure(text=f"Performance: {status}", text_color=color)

            self.logger.debug("Static performance metrics set")
        except Exception as e:
            self.logger.error(f"Static performance error: {e}")

    def _initialize_performance_monitoring(self):
        """Initialize performance monitoring systems"""
        try:
            # Initialize performance tracking
            self.performance_data = {
                'cpu_usage': 0,
                'memory_usage': 0,
                'response_time': 0,
                'model_load': 0,
                'start_time': time.time()
            }

            # Start monitoring
            self._monitor_performance()

        except Exception as e:
            self.logger.error(f"Performance monitoring initialization error: {e}")

    def _initialize_compact_chat(self):
        """Initialize compact chat for minimized window access"""
        try:
            from src.ui.compact_chat_manager import CompactChatManager

            # Create compact chat manager
            self.compact_chat = CompactChatManager(self, self.gideon_core)

            self.logger.info("✅ Compact chat initialized - chat accessible when minimized")

            # Modify window minimize behavior
            self.root.protocol("WM_DELETE_WINDOW", self._on_window_minimize_or_close)

            # Bind minimize event
            self.root.bind('<Unmap>', self._on_window_minimize)
            self.root.bind('<Map>', self._on_window_restore)

        except ImportError as e:
            self.logger.warning(f"⚠️ Compact chat not available - missing dependencies: {e}")
            self.compact_chat = None
        except Exception as e:
            self.logger.error(f"Error initializing compact chat: {e}")
            self.compact_chat = None

    def _animate_thinking_dots(self):
        """Animate thinking dots when Gideon is processing (DISABLED - no animations)"""
        # ANIMATION DISABLED - Thinking dots animation removed to ensure static interface
        return

    def _start_thinking(self, message: str = "🧠 Processing your request..."):
        """Start thinking display (STATIC - no animations)"""
        try:
            self.thinking_active = True
            self.thinking_dot_index = 0

            # Show thinking panel (static display)
            if hasattr(self, 'thinking_container'):
                self.thinking_container.pack(
                    fill="x",
                    padx=self.design_system['spacing']['md'],
                    pady=self.design_system['spacing']['sm'],
                    before=self.thinking_container.master.winfo_children()[-1]  # Insert before quick actions
                )

            # Update thinking status (static)
            if hasattr(self, 'thinking_status'):
                self.thinking_status.configure(text=message)

            # Set static progress
            if hasattr(self, 'thinking_progress'):
                self.thinking_progress.set(0.5)  # Static 50% progress

            # Set all dots to static state (no animation)
            if hasattr(self, 'thinking_dots'):
                for dot in self.thinking_dots:
                    dot.configure(text_color=self.design_system['colors']['accent_primary'])

            self.logger.debug("Static thinking display started")

        except Exception as e:
            self.logger.error(f"Error starting static thinking display: {e}")

    def _stop_thinking(self):
        """Stop thinking display (STATIC - no animations)"""
        try:
            self.thinking_active = False

            # Hide thinking panel
            if hasattr(self, 'thinking_container'):
                self.thinking_container.pack_forget()

            # Reset dots to normal color (static)
            if hasattr(self, 'thinking_dots'):
                for dot in self.thinking_dots:
                    dot.configure(text_color=self.design_system['colors']['text_muted'])

            # Set progress to complete (static)
            if hasattr(self, 'thinking_progress'):
                self.thinking_progress.set(1.0)

            self.logger.debug("Static thinking display stopped")

        except Exception as e:
            self.logger.error(f"Error stopping static thinking display: {e}")

    def _update_thinking_status(self, message: str):
        """Update thinking status message"""
        try:
            if hasattr(self, 'thinking_status'):
                self.thinking_status.configure(text=message)
        except Exception as e:
            self.logger.error(f"Error updating thinking status: {e}")

    # Avatar system removed - no visual avatar state management
    def _on_voice_chat_start(self):
        """Handle voice chat start"""
        # Avatar removed - only update status
        self._update_status("listening", "🎤 Voice chat active")

    def _on_voice_chat_end(self):
        """Handle voice chat end"""
        # Avatar removed - only update status
        self._update_status("ready", "✅ Voice chat ended")

    def _update_avatar_state(self, state: str):
        """Avatar state update removed - no visual avatar"""
        # Avatar completely removed - no state updates needed
        pass

    def _update_voice_chat_state(self, state: str):
        """Update voice chat interface state"""
        if self.voice_chat_interface:
            if state == "listening":
                self.voice_chat_interface.set_listening_state()
            elif state == "processing":
                self.voice_chat_interface.set_processing_state()
            elif state == "speaking":
                self.voice_chat_interface.set_speaking_state()
            else:
                self.voice_chat_interface.set_ready_state()

    def _update_char_counter(self, event=None):
        """Update character counter"""
        try:
            if hasattr(self, 'char_counter') and hasattr(self, 'input_entry'):
                char_count = len(self.input_entry.get())
                self.char_counter.configure(text=f"{char_count} characters")

                # Show typing indicator
                if char_count > 0 and hasattr(self, 'typing_indicator'):
                    self.typing_indicator.configure(text="✏️ Typing...")
                elif hasattr(self, 'typing_indicator'):
                    self.typing_indicator.configure(text="")

        except Exception as e:
            self.logger.error(f"Character counter update error: {e}")

    def _on_input_focus(self, event=None):
        """Handle input focus event"""
        try:
            if hasattr(self, 'input_entry') and CUSTOMTKINTER_AVAILABLE:
                # Add focus ring effect
                self.input_entry.configure(border_color=self.design_system['colors']['border_accent'])
        except:
            pass

    def _on_input_blur(self, event=None):
        """Handle input blur event"""
        try:
            if hasattr(self, 'input_entry') and CUSTOMTKINTER_AVAILABLE:
                # Remove focus ring effect
                self.input_entry.configure(border_color=self.design_system['colors']['border_default'])
        except:
            pass

    # Professional message handling and UI updates
    def _stream_response_effect(self, response: str):
        """Display response immediately (NO ANIMATIONS - permanent visibility)"""
        try:
            self.logger.info(f"📝 Displaying response immediately: '{response[:30]}...'")

            # IMMEDIATE DISPLAY - no delays or animations for permanent visibility
            self._add_message("Gideon", response, self.design_system['colors']['accent_success'])
            self.logger.info("✅ Response displayed immediately")

        except Exception as e:
            self.logger.error(f"Error displaying response: {e}")
            # Fallback: display message immediately
            self._add_message("Gideon", response, self.design_system['colors']['accent_success'])

    def _add_message(self, sender: str, message: str, color: str = None):
        """Add message to chat display with clean, reliable formatting"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")

            if color is None:
                color = self.design_system['colors']['text_primary']

            # Check if chat display exists
            if not hasattr(self, 'chat_display') or self.chat_display is None:
                self.logger.error("❌ Chat display widget not found!")
                print(f"FALLBACK: {sender}: {message}")
                return

            # Professional message formatting with enhanced styling
            if sender == "You":
                prefix = "👤"
                sender_color = self.design_system['colors']['accent_primary']
                message_style = "USER"
            elif sender == "Gideon":
                prefix = "🤖"
                sender_color = self.design_system['colors']['accent_success']
                message_style = "AI"
            elif sender == "System":
                prefix = "⚙️"
                sender_color = self.design_system['colors']['text_secondary']
                message_style = "SYS"
            else:
                prefix = "💬"
                sender_color = self.design_system['colors']['text_muted']
                message_style = "MSG"

            # Detect text direction and format accordingly
            from src.utils.text_direction import text_direction_manager as tdm
            formatted_message, direction = tdm.format_text_for_display(message)

            # Create directional message format
            if direction == "rtl":
                # RTL format: message - sender - timestamp (right-aligned)
                display_message = f"{formatted_message} :{sender} {prefix} [{timestamp}]\n"
                tag_name = f"rtl_{message_style.lower()}"
                justify = 'right'
            else:
                # LTR format: timestamp - sender - message (left-aligned)
                display_message = f"[{timestamp}] {prefix} {sender}: {formatted_message}\n"
                tag_name = f"ltr_{message_style.lower()}"
                justify = 'left'

            # Simple, reliable message insertion
            try:
                # Insert the message
                self.chat_display.insert("end", display_message)

                # Auto-scroll to bottom
                try:
                    self.chat_display.yview_moveto(1.0)
                except Exception:
                    pass

                # Update display
                try:
                    self.chat_display.update_idletasks()
                except Exception:
                    pass

            except Exception as e:
                self.logger.error(f"Error inserting message: {e}")
                # Console fallback
                print(f"{sender}: {message}")

        except Exception as e:
            self.logger.error(f"❌ Critical error in _add_message: {e}")
            print(f"CRITICAL FALLBACK: {sender}: {message}")
            import traceback
            traceback.print_exc()

    def _update_status(self, status_type: str, message: str):
        """Update status with professional styling"""
        self.current_status = status_type

        if hasattr(self, 'main_status_label'):
            status_colors = {
                "ready": self.design_system['colors']['accent_success'],
                "listening": self.design_system['colors']['accent_primary'],
                "processing": self.design_system['colors']['accent_warning'],
                "speaking": self.design_system['colors']['accent_success'],
                "error": self.design_system['colors']['accent_error'],
                "testing": self.design_system['colors']['accent_cyan'],
                "stopped": self.design_system['colors']['accent_error']
            }

            color = status_colors.get(status_type, self.design_system['colors']['text_secondary'])

            if CUSTOMTKINTER_AVAILABLE:
                self.main_status_label.configure(text=message, text_color=color)
            else:
                self.main_status_label.configure(text=message, fg=color)

    # Event handlers and functionality methods
    def _send_message(self):
        """Send text message with clean, reliable processing"""
        try:
            # Debug logging
            self.logger.info("🔥 _send_message called!")

            if not hasattr(self, 'input_entry') or not self.input_entry:
                self.logger.error("❌ Input entry widget not found!")
                return

            message = self.input_entry.get().strip()
            self.logger.info(f"📝 Message retrieved: '{message}'")

            if not message:
                self.logger.info("⚠️ Empty message, returning")
                return

            # Clear input
            self.input_entry.delete(0, tk.END)
            if hasattr(self, '_update_char_counter'):
                self._update_char_counter()

            self.logger.info(f"💬 Processing message: '{message}'")

            # Display user message
            self._add_message("You", message, self.design_system['colors']['accent_primary'])

            # Update status
            self._update_status("processing", "🧠 Processing your request...")

        except Exception as e:
            self.logger.error(f"❌ Error in _send_message: {e}")
            import traceback
            traceback.print_exc()

        # Process with AI in background thread
        def process_message():
            try:
                response = None

                # Detect input language for proper AI response
                try:
                    from src.utils.text_direction import text_direction_manager
                    input_language = "ar" if text_direction_manager.is_arabic_text(message) else "en"
                    self.logger.info(f"🌍 Detected input language: {input_language}")
                except Exception as e:
                    input_language = "en"
                    self.logger.warning(f"Language detection error: {e}")

                # Try to get AI response with language awareness
                if self.gideon_core and hasattr(self.gideon_core, 'ai_engine') and self.gideon_core.ai_engine:
                    try:
                        # Set AI engine response language based on input
                        if hasattr(self.gideon_core.ai_engine, 'set_response_language'):
                            self.gideon_core.ai_engine.set_response_language(input_language)

                        response = self.gideon_core.ai_engine.generate_response(message)
                        self.logger.info(f"🤖 AI response generated: {response[:50]}...")

                    except Exception as e:
                        self.logger.error(f"AI engine error: {e}")

                # Fallback to language-appropriate response if no AI response
                if not response or not response.strip():
                    if input_language == "ar":
                        response = "مرحباً! أنا جيديون، مساعدتك الذكية. كيف يمكنني مساعدتك اليوم؟"
                    else:
                        response = "Hello! I'm Gideon, your AI assistant. How can I help you today?"
                    self.logger.info(f"🔄 Using fallback response in {input_language}")

                # Apply gender consistency
                try:
                    from src.utils.gender_consistency import gideon_identity
                    corrected_response, _ = gideon_identity.validate_and_correct(response)
                    response = corrected_response
                except Exception as e:
                    self.logger.warning(f"Gender consistency error: {e}")

                # Display response on main thread
                self.root.after(0, lambda: self._display_ai_response(response))

            except Exception as e:
                self.logger.error(f"Error processing message: {e}")
                # Final fallback with language detection
                try:
                    from src.utils.text_direction import text_direction_manager
                    if text_direction_manager.is_arabic_text(message):
                        fallback_response = "عذراً، واجهت خطأ. يرجى المحاولة مرة أخرى."
                    else:
                        fallback_response = "I'm sorry, I encountered an error. Please try again."
                except:
                    fallback_response = "I'm sorry, I encountered an error. Please try again."

                self.root.after(0, lambda: self._display_ai_response(fallback_response))

        # Start processing in background
        threading.Thread(target=process_message, daemon=True).start()

    def _display_ai_response(self, response: str):
        """Display AI response on main thread"""
        try:
            # Add AI response to chat
            self._add_message("Gideon", response, self.design_system['colors']['accent_success'])

            # Update status
            self._update_status("ready", "✅ Ready")

            # Update voice chat state if available
            if hasattr(self, '_update_voice_chat_state'):
                self._update_voice_chat_state("ready")

        except Exception as e:
            self.logger.error(f"Error displaying AI response: {e}")
            print(f"AI Response: {response}")  # Fallback to console

    def _toggle_voice_input(self):
        """Toggle voice input with professional feedback and thinking animation"""
        if not self.gideon_core:
            self._add_message("System", "Voice input not available", self.design_system['colors']['accent_error'])
            return

        if self.is_voice_active:
            # Stop voice input
            self.is_voice_active = False
            self._update_voice_button()
            self._stop_thinking()
            self._update_status("ready", "✅ Voice input stopped")
        else:
            # Start voice input
            self.is_voice_active = True
            self._update_voice_button()
            self._start_thinking("🎤 Listening for your voice...")
            self._update_status("listening", "🎤 Listening for voice input...")
            self.gideon_core.process_voice_input(self._on_voice_response)

    def _update_voice_button(self):
        """Update voice button with professional styling"""
        if self.is_voice_active:
            if CUSTOMTKINTER_AVAILABLE:
                self.voice_button.configure(
                    text="🔴",
                    fg_color=self.design_system['colors']['accent_error']
                )
            else:
                self.voice_button.configure(
                    text="🔴",
                    bg=self.design_system['colors']['accent_error']
                )
        else:
            if CUSTOMTKINTER_AVAILABLE:
                self.voice_button.configure(
                    text="🎤",
                    fg_color=self.design_system['colors']['accent_primary']
                )
            else:
                self.voice_button.configure(
                    text="🎤",
                    bg=self.design_system['colors']['accent_primary']
                )

    def _focus_chat(self):
        """Focus on chat input with professional feedback"""
        self.input_entry.focus_set()
        self._add_message("System", "💬 Chat focused - Ready for input", self.design_system['colors']['text_muted'])

    def _take_screenshot(self):
        """Take screenshot with professional handling"""
        if self.gideon_core:
            self._update_status("processing", "📸 Taking screenshot...")
            filepath = self.gideon_core.take_screenshot()
            if filepath:
                self._add_message("System", f"📸 Screenshot saved: {filepath}", self.design_system['colors']['accent_success'])
                self._update_status("ready", "✅ Screenshot completed")
            else:
                self._add_message("System", "❌ Failed to take screenshot", self.design_system['colors']['accent_error'])
                self._update_status("error", "❌ Screenshot failed")
        else:
            self._add_message("System", "❌ Screenshot functionality not available", self.design_system['colors']['accent_error'])

    def _clear_chat(self):
        """Clear chat display with professional confirmation"""
        if CUSTOMTKINTER_AVAILABLE:
            self.chat_display.delete("0.0", tk.END)
        else:
            self.chat_display.delete("1.0", tk.END)

        self._add_message("System", "🗑️ Chat history cleared", self.design_system['colors']['text_muted'])
        self._update_status("ready", "✅ Chat cleared")

    def _emergency_stop(self):
        """Emergency stop all operations"""
        self.is_voice_active = False
        self._update_voice_button()

        if self.gideon_core:
            # Stop any ongoing operations
            try:
                if hasattr(self.gideon_core, 'stop_all_operations'):
                    self.gideon_core.stop_all_operations()
            except:
                pass

        self._add_message("System", "🛑 Emergency stop activated - All operations halted", self.design_system['colors']['accent_error'])
        self._update_status("stopped", "🛑 Emergency stop")

    def _open_settings(self):
        """Open professional settings window"""
        self._add_message("System", "⚙️ Opening enterprise settings...", self.design_system['colors']['text_secondary'])
        messagebox.showinfo("Enterprise Settings", "Professional settings panel coming soon!")

    def _open_analytics(self):
        """Open analytics dashboard"""
        self._add_message("System", "📊 Opening performance analytics...", self.design_system['colors']['accent_cyan'])
        messagebox.showinfo("Analytics Dashboard", "Performance analytics dashboard coming soon!")

    def _export_chat(self):
        """Export chat with professional formatting"""
        try:
            from tkinter import filedialog

            filename = filedialog.asksaveasfilename(
                title="Export Chat History",
                defaultextension=".txt",
                filetypes=[
                    ("Text files", "*.txt"),
                    ("Markdown files", "*.md"),
                    ("All files", "*.*")
                ]
            )

            if filename:
                if CUSTOMTKINTER_AVAILABLE:
                    content = self.chat_display.get("0.0", tk.END)
                else:
                    content = self.chat_display.get("1.0", tk.END)

                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("# Gideon AI Assistant - Chat Export\n\n")
                    f.write(f"Exported on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                    f.write("---\n\n")
                    f.write(content)

                self._add_message("System", f"📤 Chat exported to: {filename}", self.design_system['colors']['accent_success'])

        except Exception as e:
            self._add_message("System", f"❌ Export failed: {e}", self.design_system['colors']['accent_error'])

    def _show_chat_history(self):
        """Show chat history in professional format"""
        self._add_message("System", "📋 Chat history feature coming soon", self.design_system['colors']['text_secondary'])

    def _show_help(self):
        """Show professional help dialog"""
        help_text = """🤖 GIDEON AI ASSISTANT - ENTERPRISE EDITION

⌨️ KEYBOARD SHORTCUTS:
• Ctrl+N: Clear chat
• Ctrl+S: Export chat
• Ctrl+M: Open model manager
• F1: Show this help
• Enter: Send message

🧭 NAVIGATION:
• 💬 Conversation: Chat interface
• 🤖 AI Models: Manage AI models
• 🎤 Voice Control: Voice settings
• 📊 Analytics: Performance data
• 📸 Screenshot: Capture screen
• ⚙️ Settings: Configuration

⚡ QUICK ACTIONS:
• 🌍 Language: Switch English/Arabic
• 🗑️ Clear: Clear chat history
• 🛑 Stop: Emergency stop

💡 FEATURES:
• Real-time AI monitoring
• Performance analytics
• Professional animations
• Enterprise-grade design
• Advanced voice control
• Bilingual support"""
        messagebox.showinfo("Enterprise Help", help_text)

    def _open_model_manager(self):
        """Open AI model selector with professional integration"""
        try:
            from src.ui.model_selector import ModelSelectorDialog

            def on_model_changed(backend, model):
                self._add_message("System", f"🧠 AI Model changed: {model} via {backend}", self.design_system['colors']['accent_success'])
                if self.gideon_core and self.gideon_core.tts_engine:
                    self.gideon_core.tts_engine.speak(f"AI model loaded: {model}")
                self._update_status("ready", "✅ Model updated")

            if self.gideon_core and hasattr(self.gideon_core, 'ai_engine'):
                dialog = ModelSelectorDialog(
                    self.root,
                    self.gideon_core.ai_engine,
                    on_model_changed
                )
                result = dialog.show()

                if result:
                    self.logger.info(f"Model configuration updated: {result}")
                    self._add_message("System", "✅ AI model configuration updated", self.design_system['colors']['accent_success'])
            else:
                self._add_message("System", "❌ AI engine not available", self.design_system['colors']['accent_error'])

        except ImportError as e:
            self.logger.error(f"Failed to import model selector: {e}")
            self._add_message("System", "❌ Model selector not available", self.design_system['colors']['accent_error'])
        except Exception as e:
            self.logger.error(f"Error opening model selector: {e}")
            self._add_message("System", f"❌ Model selector error: {e}", self.design_system['colors']['accent_error'])

    def _test_speech(self):
        """Test speech functionality with professional feedback"""
        try:
            if not self.gideon_core:
                messagebox.showerror("Error", "Gideon core not available")
                return

            self._add_message("System", "🔊 Testing enterprise speech systems...", self.design_system['colors']['accent_warning'])
            self._update_status("testing", "🧪 Running speech tests...")

            # Test TTS
            if self.gideon_core.tts_engine and self.gideon_core.tts_engine.is_available():
                self.gideon_core.tts_engine.speak("Enterprise text to speech test. All systems operational.")
                self._add_message("System", "✅ Text-to-speech test completed", self.design_system['colors']['accent_success'])
            else:
                self._add_message("System", "❌ Text-to-speech not available", self.design_system['colors']['accent_error'])

            # Test STT
            if self.gideon_core.stt_engine and self.gideon_core.stt_engine.is_available():
                self._add_message("System", "🎤 Say 'Hello Gideon' to test speech recognition...", self.design_system['colors']['accent_primary'])

                def test_callback(user_input, ai_response):
                    if user_input:
                        self._add_message("System", f"✅ Speech recognized: '{user_input}'", self.design_system['colors']['accent_success'])
                        if "gideon" in user_input.lower():
                            self._add_message("System", "🎯 Wake word detection successful!", self.design_system['colors']['accent_success'])
                        self._update_status("ready", "✅ Speech test completed")
                    else:
                        self._add_message("System", "❌ No speech detected", self.design_system['colors']['accent_error'])
                        self._update_status("ready", "⚠️ Speech test incomplete")

                self.gideon_core.process_voice_input(test_callback)
            else:
                self._add_message("System", "❌ Speech recognition not available", self.design_system['colors']['accent_error'])
                self._update_status("ready", "⚠️ Speech test incomplete")

        except Exception as e:
            self.logger.error(f"Error testing speech: {e}")
            self._add_message("System", f"❌ Speech test error: {e}", self.design_system['colors']['accent_error'])
            self._update_status("error", "❌ Speech test failed")

    def _toggle_language(self):
        """Toggle between English and Arabic with professional handling"""
        try:
            current_lang = self.config.get("app.language", "en")

            if current_lang == "en":
                # Switch to Arabic
                self.config.set("app.language", "ar")
                self.i18n.set_language("ar")

                # CRITICAL FIX: Set AI engine response language to Arabic
                if self.gideon_core and self.gideon_core.ai_engine:
                    self.gideon_core.ai_engine.set_response_language("ar")
                    self.logger.info("🌍 AI engine response language set to Arabic")

                self._add_message("System", "تم تغيير اللغة إلى العربية 🇸🇦", self.design_system['colors']['accent_success'])

                if self.gideon_core and self.gideon_core.tts_engine:
                    self.gideon_core.tts_engine.speak("مرحباً! تم تغيير اللغة إلى العربية")

                if CUSTOMTKINTER_AVAILABLE:
                    self.language_button.configure(text="🌍 English")
                else:
                    self.language_button.configure(text="🌍 English")

            else:
                # Switch to English
                self.config.set("app.language", "en")
                self.i18n.set_language("en")

                # CRITICAL FIX: Set AI engine response language to English
                if self.gideon_core and self.gideon_core.ai_engine:
                    self.gideon_core.ai_engine.set_response_language("en")
                    self.logger.info("🌍 AI engine response language set to English")

                self._add_message("System", "Language changed to English 🇺🇸", self.design_system['colors']['accent_success'])

                if self.gideon_core and self.gideon_core.tts_engine:
                    self.gideon_core.tts_engine.speak("Language changed to English. Enterprise systems ready.")

                if CUSTOMTKINTER_AVAILABLE:
                    self.language_button.configure(text="🌍 عربي/AR")
                else:
                    self.language_button.configure(text="🌍 عربي/AR")

            # Reinitialize TTS with new language
            if self.gideon_core and self.gideon_core.tts_engine:
                self.gideon_core.tts_engine._set_voice()

            self._update_status("ready", "✅ Language updated")

        except Exception as e:
            self.logger.error(f"Error toggling language: {e}")
            self._add_message("System", f"❌ Language toggle error: {e}", self.design_system['colors']['accent_error'])

    # Callback methods for Gideon core integration
    def _on_text_response(self, user_input: str, ai_response: str):
        """Handle text response with ultra-professional feedback and consistency"""
        # Ensure this runs on the main thread
        if hasattr(self, 'root') and self.root:
            self.root.after(0, lambda: self._handle_text_response_main_thread(user_input, ai_response))
        else:
            self._handle_text_response_main_thread(user_input, ai_response)

    def _handle_text_response_main_thread(self, user_input: str, ai_response: str):
        """Handle text response on main thread"""
        try:
            self.logger.info(f"🔄 Handling text response on main thread: '{user_input}' -> '{ai_response[:50]}...'")

            # Stop thinking animation - avatar removed
            self._stop_thinking()
            # Avatar removed - no state updates
            self._update_voice_chat_state("speaking")

            if ai_response and ai_response.strip():
                # Apply gender consistency if not already applied
                corrected_response, is_consistent = gideon_identity.validate_and_correct(ai_response)

                self.logger.info(f"✅ Displaying AI response: '{corrected_response[:50]}...'")

                # Display response with professional formatting
                self.logger.info(f"🚀 DISPLAYING MESSAGE: '{corrected_response[:30]}...'")

                # Choose display method based on response length
                if len(corrected_response) > 50:  # Use streaming effect for longer responses
                    self.logger.info("📝 Using streaming effect for long response")
                    self._stream_response_effect(corrected_response)
                else:
                    # Direct display for shorter responses
                    self.logger.info("📝 Direct display for short response")
                    self._add_message("Gideon", corrected_response, self.design_system['colors']['accent_success'])

                # FORCE WIDGET UPDATE
                if hasattr(self, 'chat_display') and self.chat_display:
                    try:
                        self.chat_display.update()
                        self.chat_display.update_idletasks()
                    except:
                        pass

                # FORCE ROOT UPDATE
                if hasattr(self, 'root') and self.root:
                    try:
                        self.root.update()
                        self.root.update_idletasks()
                    except:
                        pass

                # Speak the response if TTS is available
                if self.gideon_core and self.gideon_core.tts_engine:
                    try:
                        self.gideon_core.tts_engine.speak(corrected_response)
                    except Exception as e:
                        self.logger.error(f"Error speaking response: {e}")

                # Update status
                self._update_status("ready", "✅ Response completed")
            else:
                self._add_message("Gideon", "I'm sorry, I couldn't generate a response.", self.design_system['colors']['accent_error'])
                self._update_status("error", "❌ No response generated")

            # Avatar removed - no state updates
            self._update_voice_chat_state("idle")

        except Exception as e:
            self.logger.error(f"Error handling text response: {e}")
            self._add_message("System", f"❌ Error displaying response: {e}", self.design_system['colors']['accent_error'])
            self._update_status("error", "❌ Display error")

    def _on_response_chunk(self, chunk: str, is_final: bool):
        """Handle streamed response chunks"""
        # This could be used to update UI with streaming text
        # For now, we'll just log it
        if is_final:
            self.logger.debug("Response streaming completed")

    def _on_voice_response(self, user_input: str, ai_response: str):
        """Handle voice response with professional feedback"""
        # Ensure this runs on the main thread
        if hasattr(self, 'root') and self.root:
            self.root.after(0, lambda: self._handle_voice_response_main_thread(user_input, ai_response))
        else:
            self._handle_voice_response_main_thread(user_input, ai_response)

    def _handle_voice_response_main_thread(self, user_input: str, ai_response: str):
        """Handle voice response on main thread"""
        try:
            self.is_voice_active = False
            self._update_voice_button()
            self._stop_thinking()

            if user_input:
                self._add_message("You", f"🎤 {user_input}", self.design_system['colors']['accent_primary'])

                # Start thinking for AI response processing
                if ai_response:
                    self._start_thinking("🧠 Processing voice response...")
                    self.root.after(500, lambda: self._update_thinking_status("🗣️ Preparing to speak..."))
                    self.root.after(1000, lambda: (
                        self._stop_thinking(),
                        self._add_message("Gideon", ai_response, self.design_system['colors']['accent_success']),
                        self._update_status("ready", "✅ Voice interaction completed")
                    ))
                else:
                    self._add_message("Gideon", "I heard you but couldn't process that.", self.design_system['colors']['accent_warning'])
                    self._update_status("ready", "⚠️ Voice processing incomplete")
            else:
                self._add_message("System", "🎤 No voice input detected", self.design_system['colors']['text_muted'])
                self._update_status("ready", "⚠️ No voice detected")

        except Exception as e:
            self.logger.error(f"Error handling voice response: {e}")
            self._add_message("System", f"❌ Error processing voice: {e}", self.design_system['colors']['accent_error'])
            self._update_status("error", "❌ Voice processing error")

    def _on_response_ready(self, user_input: str, response: str):
        """Handle when response is ready - DISABLED to prevent duplicate responses"""
        # This method is disabled to prevent duplicate responses
        # Response display is handled by _handle_text_response_main_thread
        self.logger.info(f"📝 Response ready callback received (ignored to prevent duplicates): '{response[:30]}...'")
        if response:
            self._update_status("ready", "✅ Response ready")

    def _on_status_change(self, status: str):
        """Handle status change with professional updates"""
        status_messages = {
            "listening": "🎤 Listening for voice input...",
            "processing": "🧠 Processing with AI engine...",
            "speaking": "🔊 Speaking response...",
            "ready": "✅ System ready",
            "error": "❌ System error",
            "offline": "⚠️ System offline"
        }

        message = status_messages.get(status, f"Status: {status}")
        self._update_status(status, message)

    def _on_error(self, error_message: str):
        """Handle error with professional error display"""
        self._add_message("System", f"❌ Error: {error_message}", self.design_system['colors']['accent_error'])
        self._update_status("error", f"❌ {error_message}")

    def _on_window_minimize_or_close(self):
        """Handle window minimize to compact chat or close"""
        try:
            if self.compact_chat:
                # Show compact chat instead of closing
                self.compact_chat.show_compact_chat()
                self.minimized_to_compact = True

                # Minimize the main window
                self.root.withdraw()

                self.logger.info("💬 Window minimized - compact chat shown for continued access")
            else:
                # No compact chat available, proceed with normal close
                self._on_window_close()

        except Exception as e:
            self.logger.error(f"Error during window minimize/close: {e}")
            self._on_window_close()

    def _on_window_minimize(self, event=None):
        """Handle window minimize event"""
        try:
            if event and event.widget == self.root:
                if self.compact_chat and not self.compact_chat.is_compact_visible():
                    # Show compact chat when minimized
                    self.compact_chat.show_compact_chat()
                    self.minimized_to_compact = True
                    self.logger.info("💬 Window minimized - compact chat activated")

        except Exception as e:
            self.logger.error(f"Error handling window minimize: {e}")

    def _on_window_restore(self, event=None):
        """Handle window restore event"""
        try:
            if event and event.widget == self.root:
                if self.compact_chat and self.compact_chat.is_compact_visible():
                    # Hide compact chat when main window is restored
                    self.compact_chat.hide_compact_chat()
                    self.minimized_to_compact = False
                    self.logger.info("🔄 Window restored - compact chat hidden")

        except Exception as e:
            self.logger.error(f"Error handling window restore: {e}")

    def _on_window_close(self):
        """Handle actual window close with static interface cleanup"""
        try:
            # Animations already disabled - no animation cleanup needed
            self.animation_running = False

            # Hide compact chat if running
            if self.compact_chat:
                self.compact_chat.hide_compact_chat()

            # Cleanup new components
            # Avatar manager removed - no cleanup needed

            if self.voice_chat_interface:
                self.voice_chat_interface.destroy()

            if self.performance_manager:
                self.performance_manager.shutdown()

            if self.gideon_core:
                self.gideon_core.shutdown()

            self.is_running = False
            self.root.quit()
            self.root.destroy()

            self.logger.info("✅ Static interface cleanup completed - chat box was permanently visible")

        except Exception as e:
            self.logger.error(f"Error during window close: {e}")
            self.root.destroy()

    def _show_compact_chat_manual(self):
        """Manually show compact chat window"""
        try:
            if self.compact_chat:
                self.compact_chat.show_compact_chat()
                self.minimized_to_compact = True
                self._add_message("System", "💬 Compact chat window opened - chat accessible when main window is minimized.", self.design_system['colors']['accent_success'])
                self.logger.info("💬 Compact chat manually opened")
            else:
                self._add_message("System", "⚠️ Compact chat not available.", self.design_system['colors']['accent_warning'])

        except Exception as e:
            self.logger.error(f"Error showing compact chat: {e}")
            self._add_message("System", f"❌ Error showing compact chat: {e}", self.design_system['colors']['accent_error'])

    def restore_from_compact(self):
        """Restore window from compact chat (called by compact chat manager)"""
        try:
            if self.compact_chat:
                self.compact_chat.hide_compact_chat()
                self.minimized_to_compact = False

                # Ensure main window is visible
                self.root.deiconify()
                self.root.lift()
                self.root.focus_force()

                self.logger.info("🔄 Window restored from compact chat")

        except Exception as e:
            self.logger.error(f"Error restoring from compact chat: {e}")

    def get_chat_content(self):
        """Get current chat content for compact chat sync"""
        try:
            if hasattr(self, 'chat_display') and self.chat_display:
                return self.chat_display.get("0.0", "end")
            return ""
        except Exception as e:
            self.logger.error(f"Error getting chat content: {e}")
            return ""

    def add_message_from_compact(self, sender: str, message: str, color: str = None):
        """Add message from compact chat to main chat"""
        try:
            if color is None:
                color = self.design_system['colors']['text_primary']
            self._add_message(sender, message, color)
        except Exception as e:
            self.logger.error(f"Error adding message from compact chat: {e}")

    def update_compact_chat_status(self, status: str, color: str = None):
        """Update status in compact chat if visible"""
        try:
            if self.compact_chat and self.compact_chat.is_compact_visible():
                self.compact_chat.update_compact_status(status, color)
        except Exception as e:
            self.logger.error(f"Error updating compact chat status: {e}")

    def run(self):
        """Run the ultra-professional interface"""
        try:
            self.is_running = True

            # Add professional welcome messages
            self._add_message("System", "🚀 Gideon AI Assistant Enterprise Edition initialized!", self.design_system['colors']['accent_success'])
            self._add_message("System", "💼 Ultra-professional interface active with advanced features", self.design_system['colors']['text_secondary'])
            self._add_message("System", "🎯 Real-time monitoring, performance analytics, and enterprise tools ready", self.design_system['colors']['accent_primary'])

            # Check and display AI status
            if self.gideon_core and hasattr(self.gideon_core, 'ai_engine'):
                if self.gideon_core.ai_engine.llm_enabled:
                    model_name = self.gideon_core.ai_engine.active_backend.current_model if self.gideon_core.ai_engine.active_backend else "Unknown"
                    self._add_message("System", f"🧠 Enterprise AI Model: {model_name}", self.design_system['colors']['accent_success'])
                    self._add_message("System", "💡 Advanced AI capabilities ready for complex enterprise tasks", self.design_system['colors']['text_secondary'])
                else:
                    self._add_message("System", "⚠️ Rule-based mode active. Configure AI models for advanced capabilities.", self.design_system['colors']['accent_warning'])

            self._add_message("System", "💬 Say 'Gideon' + your question, or type in the professional interface", self.design_system['colors']['text_muted'])

            # Add test message to verify chat display is working
            self.root.after(2000, lambda: self._add_message("Gideon", "Hello! I'm Gideon, your AI assistant. I'm ready to help you!", self.design_system['colors']['accent_success']))

            # Update initial status
            self._update_status("ready", "✅ Enterprise system ready")

            # Start the main loop
            self.root.mainloop()

        except Exception as e:
            self.logger.error(f"Error running ultra-professional interface: {e}")
            messagebox.showerror("Enterprise Error", f"Failed to run interface: {e}")
        finally:
            self.animation_running = False
            self.is_running = False


def create_ultra_professional_interface(gideon_core=None):
    """Create and return ultra-professional interface"""
    interface = UltraProfessionalInterface(gideon_core)
    root = interface.create_window()
    return interface, root
